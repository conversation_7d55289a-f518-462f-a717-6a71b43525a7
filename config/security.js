/**
 * 安全配置模块
 * 专门处理所有安全相关的配置和验证
 */

const crypto = require('crypto');

/**
 * 验证必需的安全环境变量
 */
function validateSecurityEnvVars() {
    const required = [
        'JWT_SECRET',
        'SESSION_SECRET'
    ];
    
    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
        console.error('❌ 缺少必需的安全环境变量:');
        missing.forEach(key => console.error(`   - ${key}`));
        console.error('请检查 .env 文件配置');
        process.exit(1);
    }
    
    // 验证JWT密钥强度
    if (process.env.JWT_SECRET.length < 32) {
        console.error('❌ JWT_SECRET 长度不足32位，安全性不够');
        process.exit(1);
    }
}

/**
 * 生成安全的随机密钥
 */
function generateSecureKey(length = 32) {
    return crypto.randomBytes(length).toString('hex');
}

/**
 * 密码强度验证规则
 */
const passwordPolicy = {
    minLength: 8,
    maxLength: 128,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    specialChars: '!@#$%^&*()_+-=[]{}|;:,.<>?',
    
    validate(password) {
        const errors = [];
        
        if (!password || password.length < this.minLength) {
            errors.push(`密码长度至少${this.minLength}位`);
        }
        
        if (password && password.length > this.maxLength) {
            errors.push(`密码长度不能超过${this.maxLength}位`);
        }
        
        if (this.requireUppercase && !/[A-Z]/.test(password)) {
            errors.push('密码必须包含大写字母');
        }
        
        if (this.requireLowercase && !/[a-z]/.test(password)) {
            errors.push('密码必须包含小写字母');
        }
        
        if (this.requireNumbers && !/\d/.test(password)) {
            errors.push('密码必须包含数字');
        }
        
        if (this.requireSpecialChars && !new RegExp(`[${this.specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password)) {
            errors.push('密码必须包含特殊字符');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
};

/**
 * 输入验证规则
 */
const inputValidation = {
    email: {
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        maxLength: 254,
        validate(email) {
            if (!email) return { isValid: false, error: '邮箱地址不能为空' };
            if (email.length > this.maxLength) return { isValid: false, error: '邮箱地址过长' };
            if (!this.pattern.test(email)) return { isValid: false, error: '邮箱地址格式无效' };
            return { isValid: true };
        }
    },
    
    username: {
        pattern: /^[a-zA-Z0-9_-]{3,20}$/,
        validate(username) {
            if (!username) return { isValid: false, error: '用户名不能为空' };
            if (!this.pattern.test(username)) {
                return { isValid: false, error: '用户名只能包含字母、数字、下划线和连字符，长度3-20位' };
            }
            return { isValid: true };
        }
    },
    
    // XSS防护
    sanitizeHtml(input) {
        if (!input) return '';
        return input
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    },
    
    // SQL注入防护
    sanitizeSql(input) {
        if (!input) return '';
        return input.replace(/['";\\]/g, '');
    }
};

/**
 * 速率限制配置
 */
const rateLimitConfig = {
    // 通用API限制
    general: {
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 100, // 每个IP最多100个请求
        message: {
            error: '请求过于频繁，请稍后再试',
            retryAfter: '15分钟后重试'
        },
        standardHeaders: true,
        legacyHeaders: false
    },
    
    // 登录限制
    auth: {
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 5, // 每个IP最多5次登录尝试
        message: {
            error: '登录尝试过于频繁，请稍后再试',
            retryAfter: '15分钟后重试'
        },
        skipSuccessfulRequests: true
    },
    
    // 邮件查询限制
    email: {
        windowMs: 1 * 60 * 1000, // 1分钟
        max: 30, // 每分钟最多30次邮件查询
        message: {
            error: '邮件查询过于频繁，请稍后再试',
            retryAfter: '1分钟后重试'
        }
    }
};

/**
 * CORS安全配置
 */
const corsConfig = {
    origin: function (origin, callback) {
        const allowedOrigins = process.env.CORS_ORIGIN ? 
            process.env.CORS_ORIGIN.split(',').map(o => o.trim()) : 
            ['http://localhost:3000'];
            
        // 允许没有origin的请求（如移动应用）
        if (!origin) return callback(null, true);
        
        if (allowedOrigins.indexOf(origin) !== -1 || allowedOrigins.includes('*')) {
            callback(null, true);
        } else {
            callback(new Error('不被CORS策略允许的来源'));
        }
    },
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

/**
 * Helmet安全头配置
 */
const helmetConfig = {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'"],
            fontSrc: ["'self'"],
            objectSrc: ["'none'"],
            mediaSrc: ["'self'"],
            frameSrc: ["'none'"],
            baseUri: ["'self'"],
            formAction: ["'self'"]
        }
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000, // 1年
        includeSubDomains: true,
        preload: true
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true
};

/**
 * JWT配置
 */
const jwtConfig = {
    secret: process.env.JWT_SECRET,
    refreshSecret: process.env.JWT_REFRESH_SECRET || generateSecureKey(),
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    algorithm: 'HS256',
    issuer: process.env.DOMAIN,
    audience: process.env.DOMAIN,
    
    // JWT黑名单（应该用Redis实现）
    blacklist: new Set(),
    
    // 添加到黑名单
    addToBlacklist(token) {
        this.blacklist.add(token);
    },
    
    // 检查是否在黑名单
    isBlacklisted(token) {
        return this.blacklist.has(token);
    }
};

module.exports = {
    validateSecurityEnvVars,
    generateSecureKey,
    passwordPolicy,
    inputValidation,
    rateLimitConfig,
    corsConfig,
    helmetConfig,
    jwtConfig
};

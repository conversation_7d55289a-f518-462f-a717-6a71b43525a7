{"name": "temp-email-manager", "version": "1.0.0", "description": "临时邮箱管理系统 - 专为账号销售业务设计", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest --coverage", "test:watch": "jest --watch", "test:security": "npm audit && npm run test:unit", "test:unit": "jest --testPathPattern=tests/unit", "test:integration": "jest --testPathPattern=tests/integration", "test:e2e": "jest --testPathPattern=tests/e2e", "lint": "eslint server/ config/ --ext .js", "lint:fix": "eslint server/ config/ --ext .js --fix", "security:check": "npm audit --audit-level moderate", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "logs:clear": "rm -rf logs/*.log"}, "keywords": ["email", "temporary", "mailbox", "management", "system"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "pg": "^8.11.3", "pg-pool": "^3.6.1", "redis": "^4.6.8", "imap": "^0.8.19", "mailparser": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "compression": "^1.7.4", "express-slow-down": "^1.6.0", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "joi": "^17.9.2", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.46.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "@types/jest": "^29.5.3"}, "engines": {"node": ">=14.0.0"}}
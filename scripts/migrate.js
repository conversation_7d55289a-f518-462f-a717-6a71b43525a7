#!/usr/bin/env node

/**
 * 数据库迁移管理器
 * 负责执行数据库架构迁移
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

const dbConnection = require('../server/models/DatabaseConnection');
const { logger } = require('../server/utils/logger');

/**
 * 迁移管理器类
 */
class MigrationManager {
    constructor() {
        this.migrationsDir = path.join(__dirname, 'migrations');
        this.migrationTable = 'schema_migrations';
    }

    /**
     * 初始化迁移系统
     */
    async init() {
        try {
            await dbConnection.init();
            await this.createMigrationTable();
            logger.info('迁移系统初始化完成');
        } catch (error) {
            logger.error('迁移系统初始化失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 创建迁移记录表
     */
    async createMigrationTable() {
        const createTableSQL = `
            CREATE TABLE IF NOT EXISTS ${this.migrationTable} (
                id SERIAL PRIMARY KEY,
                filename VARCHAR(255) NOT NULL UNIQUE,
                checksum VARCHAR(64) NOT NULL,
                executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            );
        `;

        await dbConnection.query(createTableSQL);
        logger.info('迁移记录表已准备就绪');
    }

    /**
     * 获取所有迁移文件
     */
    getMigrationFiles() {
        if (!fs.existsSync(this.migrationsDir)) {
            logger.warn('迁移目录不存在', { dir: this.migrationsDir });
            return [];
        }

        return fs.readdirSync(this.migrationsDir)
            .filter(file => file.endsWith('.sql'))
            .sort(); // 按文件名排序
    }

    /**
     * 获取已执行的迁移
     */
    async getExecutedMigrations() {
        try {
            const result = await dbConnection.query(
                `SELECT filename, checksum FROM ${this.migrationTable} ORDER BY id`
            );
            return result.rows;
        } catch (error) {
            logger.error('获取已执行迁移失败', { error: error.message });
            return [];
        }
    }

    /**
     * 计算文件校验和
     */
    calculateChecksum(content) {
        const crypto = require('crypto');
        return crypto.createHash('sha256').update(content).digest('hex');
    }

    /**
     * 执行单个迁移
     */
    async executeMigration(filename) {
        const filePath = path.join(this.migrationsDir, filename);
        const content = fs.readFileSync(filePath, 'utf8');
        const checksum = this.calculateChecksum(content);

        logger.info('开始执行迁移', { filename, checksum });

        try {
            await dbConnection.transaction(async (client) => {
                // 执行迁移SQL
                await client.query(content);

                // 记录迁移执行
                await client.query(
                    `INSERT INTO ${this.migrationTable} (filename, checksum) VALUES ($1, $2)`,
                    [filename, checksum]
                );
            });

            logger.info('迁移执行成功', { filename });
        } catch (error) {
            logger.error('迁移执行失败', { 
                filename, 
                error: error.message,
                detail: error.detail 
            });
            throw error;
        }
    }

    /**
     * 验证迁移完整性
     */
    async validateMigrations() {
        const migrationFiles = this.getMigrationFiles();
        const executedMigrations = await this.getExecutedMigrations();

        const executedMap = new Map(
            executedMigrations.map(m => [m.filename, m.checksum])
        );

        const issues = [];

        for (const filename of migrationFiles) {
            const filePath = path.join(this.migrationsDir, filename);
            const content = fs.readFileSync(filePath, 'utf8');
            const currentChecksum = this.calculateChecksum(content);

            if (executedMap.has(filename)) {
                const recordedChecksum = executedMap.get(filename);
                if (currentChecksum !== recordedChecksum) {
                    issues.push({
                        type: 'checksum_mismatch',
                        filename,
                        message: '迁移文件已被修改'
                    });
                }
            }
        }

        // 检查是否有已执行但文件不存在的迁移
        for (const executed of executedMigrations) {
            if (!migrationFiles.includes(executed.filename)) {
                issues.push({
                    type: 'missing_file',
                    filename: executed.filename,
                    message: '迁移文件已被删除'
                });
            }
        }

        return issues;
    }

    /**
     * 运行所有待执行的迁移
     */
    async runMigrations() {
        const migrationFiles = this.getMigrationFiles();
        const executedMigrations = await this.getExecutedMigrations();
        const executedSet = new Set(executedMigrations.map(m => m.filename));

        const pendingMigrations = migrationFiles.filter(
            filename => !executedSet.has(filename)
        );

        if (pendingMigrations.length === 0) {
            logger.info('没有待执行的迁移');
            return;
        }

        logger.info('发现待执行的迁移', { 
            count: pendingMigrations.length,
            migrations: pendingMigrations 
        });

        for (const filename of pendingMigrations) {
            await this.executeMigration(filename);
        }

        logger.info('所有迁移执行完成');
    }

    /**
     * 获取迁移状态
     */
    async getStatus() {
        const migrationFiles = this.getMigrationFiles();
        const executedMigrations = await this.getExecutedMigrations();
        const executedSet = new Set(executedMigrations.map(m => m.filename));

        const pending = migrationFiles.filter(f => !executedSet.has(f));
        const executed = migrationFiles.filter(f => executedSet.has(f));

        return {
            total: migrationFiles.length,
            executed: executed.length,
            pending: pending.length,
            pendingMigrations: pending,
            executedMigrations: executed
        };
    }

    /**
     * 关闭连接
     */
    async close() {
        await dbConnection.close();
    }
}

/**
 * 主函数
 */
async function main() {
    const manager = new MigrationManager();

    try {
        await manager.init();

        const command = process.argv[2] || 'migrate';

        switch (command) {
            case 'migrate':
                // 验证迁移完整性
                const issues = await manager.validateMigrations();
                if (issues.length > 0) {
                    logger.error('迁移验证失败', { issues });
                    process.exit(1);
                }

                // 执行迁移
                await manager.runMigrations();
                break;

            case 'status':
                const status = await manager.getStatus();
                console.log('\n=== 迁移状态 ===');
                console.log(`总计: ${status.total}`);
                console.log(`已执行: ${status.executed}`);
                console.log(`待执行: ${status.pending}`);
                
                if (status.pendingMigrations.length > 0) {
                    console.log('\n待执行的迁移:');
                    status.pendingMigrations.forEach(m => console.log(`  - ${m}`));
                }
                break;

            case 'validate':
                const validationIssues = await manager.validateMigrations();
                if (validationIssues.length === 0) {
                    console.log('✅ 所有迁移验证通过');
                } else {
                    console.log('❌ 发现迁移问题:');
                    validationIssues.forEach(issue => {
                        console.log(`  - ${issue.filename}: ${issue.message}`);
                    });
                    process.exit(1);
                }
                break;

            default:
                console.log('用法: node migrate.js [migrate|status|validate]');
                console.log('  migrate  - 执行所有待执行的迁移');
                console.log('  status   - 显示迁移状态');
                console.log('  validate - 验证迁移完整性');
                break;
        }

    } catch (error) {
        logger.error('迁移过程失败', { error: error.message });
        process.exit(1);
    } finally {
        await manager.close();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main().catch(error => {
        console.error('迁移脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = MigrationManager;

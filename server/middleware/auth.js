/**
 * 认证中间件
 * 处理JWT令牌验证、权限检查等
 */

const UserService = require('../services/UserService');
const { rateLimitConfig } = require('../../config/security');

/**
 * JWT令牌认证中间件
 */
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({
                success: false,
                error: '访问令牌缺失',
                code: 'TOKEN_MISSING'
            });
        }

        const userService = new UserService();
        const authResult = await userService.verifyAccessToken(token);
        
        if (!authResult.success) {
            return res.status(401).json({
                success: false,
                error: authResult.error,
                code: authResult.code
            });
        }

        // 将用户信息添加到请求对象
        req.user = authResult.user;
        req.token = token;
        req.tokenData = authResult.decoded;
        
        next();
    } catch (error) {
        console.error('认证中间件错误:', error);
        return res.status(500).json({
            success: false,
            error: '认证服务异常',
            code: 'AUTH_SERVICE_ERROR'
        });
    }
};

/**
 * 可选的JWT认证中间件（不强制要求令牌）
 */
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (token) {
            const userService = new UserService();
            const authResult = await userService.verifyAccessToken(token);
            
            if (authResult.success) {
                req.user = authResult.user;
                req.token = token;
                req.tokenData = authResult.decoded;
            }
        }
        
        next();
    } catch (error) {
        console.error('可选认证中间件错误:', error);
        // 可选认证失败不阻止请求继续
        next();
    }
};

/**
 * 权限检查中间件
 * @param {string} requiredPermission - 需要的权限
 */
const requirePermission = (requiredPermission) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                error: '需要登录',
                code: 'LOGIN_REQUIRED'
            });
        }

        // 这里可以根据需要实现更复杂的权限检查
        // 目前简单检查用户是否有效
        if (!req.user.email) {
            return res.status(403).json({
                success: false,
                error: '权限不足',
                code: 'INSUFFICIENT_PERMISSIONS'
            });
        }

        next();
    };
};

/**
 * 邮箱域名验证中间件
 */
const validateEmailDomain = (req, res, next) => {
    try {
        const { email } = req.body;
        
        if (!email) {
            return res.status(400).json({
                success: false,
                error: '邮箱地址不能为空',
                code: 'EMAIL_REQUIRED'
            });
        }

        const userService = new UserService();
        const emailValidation = userService.validateEmailFormat(email);
        
        if (!emailValidation.success) {
            return res.status(400).json({
                success: false,
                error: emailValidation.error,
                code: emailValidation.code
            });
        }

        const domainValidation = userService.validateEmailDomain(emailValidation.sanitizedEmail);
        
        if (!domainValidation.success) {
            return res.status(400).json({
                success: false,
                error: domainValidation.error,
                code: domainValidation.code
            });
        }

        // 将验证后的邮箱添加到请求对象
        req.validatedEmail = emailValidation.sanitizedEmail;
        
        next();
    } catch (error) {
        console.error('邮箱域名验证中间件错误:', error);
        return res.status(500).json({
            success: false,
            error: '邮箱验证服务异常',
            code: 'EMAIL_VALIDATION_ERROR'
        });
    }
};

/**
 * 账户锁定检查中间件
 */
const checkAccountLockout = (req, res, next) => {
    try {
        const identifier = req.ip || req.connection.remoteAddress;
        const userService = new UserService();
        const lockoutStatus = userService.checkAccountLockout(identifier);
        
        if (lockoutStatus.isLocked) {
            return res.status(429).json({
                success: false,
                error: lockoutStatus.message,
                code: 'ACCOUNT_LOCKED',
                retryAfter: lockoutStatus.remainingTime
            });
        }
        
        // 将标识符添加到请求对象，供后续使用
        req.clientIdentifier = identifier;
        
        next();
    } catch (error) {
        console.error('账户锁定检查中间件错误:', error);
        next(); // 不阻止请求继续，但记录错误
    }
};

/**
 * 安全头中间件
 */
const securityHeaders = (req, res, next) => {
    // 防止点击劫持
    res.setHeader('X-Frame-Options', 'DENY');
    
    // 防止MIME类型嗅探
    res.setHeader('X-Content-Type-Options', 'nosniff');
    
    // XSS保护
    res.setHeader('X-XSS-Protection', '1; mode=block');
    
    // 引用者策略
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // 权限策略
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    next();
};

/**
 * 请求日志中间件
 */
const requestLogger = (req, res, next) => {
    const start = Date.now();
    const originalSend = res.send;
    
    res.send = function(data) {
        const duration = Date.now() - start;
        const logData = {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            timestamp: new Date().toISOString()
        };
        
        // 记录用户信息（如果已认证）
        if (req.user) {
            logData.user = req.user.email;
        }
        
        console.log('API请求:', JSON.stringify(logData));
        originalSend.call(this, data);
    };
    
    next();
};

module.exports = {
    authenticateToken,
    optionalAuth,
    requirePermission,
    validateEmailDomain,
    checkAccountLockout,
    securityHeaders,
    requestLogger
};

/**
 * 性能监控中间件
 * 监控API响应时间、内存使用、数据库查询等性能指标
 */

const { logger, logUtils, performanceLogger } = require('../utils/logger');
const cacheManager = require('../utils/cache');

/**
 * API响应时间监控中间件
 */
const responseTimeMonitor = (req, res, next) => {
    const start = process.hrtime.bigint();
    
    // 重写res.end方法来捕获响应完成时间
    const originalEnd = res.end;
    res.end = function(...args) {
        const end = process.hrtime.bigint();
        const duration = Number(end - start) / 1000000; // 转换为毫秒
        
        // 记录性能指标
        const performanceData = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            duration: Math.round(duration * 100) / 100, // 保留2位小数
            userAgent: req.get('User-Agent'),
            ip: req.ip,
            timestamp: new Date().toISOString()
        };
        
        // 如果响应时间超过阈值，记录警告
        const slowThreshold = parseInt(process.env.SLOW_REQUEST_THRESHOLD) || 1000;
        if (duration > slowThreshold) {
            logger.warn('慢请求检测', {
                ...performanceData,
                threshold: slowThreshold
            });
        }
        
        // 记录到性能日志
        performanceLogger.info('API响应时间', performanceData);
        
        // 调用原始的end方法
        originalEnd.apply(this, args);
    };
    
    next();
};

/**
 * 内存使用监控中间件
 */
const memoryMonitor = (req, res, next) => {
    const memUsage = process.memoryUsage();
    
    // 将字节转换为MB
    const memoryData = {
        rss: Math.round(memUsage.rss / 1024 / 1024 * 100) / 100,
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100,
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100,
        external: Math.round(memUsage.external / 1024 / 1024 * 100) / 100,
        arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024 * 100) / 100
    };
    
    // 检查内存使用是否超过阈值
    const memoryThreshold = parseInt(process.env.MEMORY_THRESHOLD_MB) || 512;
    if (memoryData.heapUsed > memoryThreshold) {
        logger.warn('内存使用过高', {
            ...memoryData,
            threshold: memoryThreshold,
            url: req.url
        });
    }
    
    // 将内存信息添加到请求对象
    req.memoryUsage = memoryData;
    
    next();
};

/**
 * 请求大小监控中间件
 */
const requestSizeMonitor = (req, res, next) => {
    const contentLength = req.get('Content-Length');
    
    if (contentLength) {
        const sizeInMB = parseInt(contentLength) / 1024 / 1024;
        const sizeThreshold = parseInt(process.env.REQUEST_SIZE_THRESHOLD_MB) || 10;
        
        if (sizeInMB > sizeThreshold) {
            logger.warn('大请求检测', {
                url: req.url,
                method: req.method,
                sizeInMB: Math.round(sizeInMB * 100) / 100,
                threshold: sizeThreshold,
                ip: req.ip
            });
        }
        
        req.requestSize = sizeInMB;
    }
    
    next();
};

/**
 * 数据库连接池监控中间件
 */
const dbPoolMonitor = async (req, res, next) => {
    try {
        const dbConnection = require('../models/DatabaseConnection');
        const poolStatus = dbConnection.getPoolStatus();
        
        // 检查连接池使用率
        if (poolStatus.connected && poolStatus.totalCount) {
            const usageRate = (poolStatus.totalCount - poolStatus.idleCount) / poolStatus.totalCount;
            const usageThreshold = parseFloat(process.env.DB_POOL_USAGE_THRESHOLD) || 0.8;
            
            if (usageRate > usageThreshold) {
                logger.warn('数据库连接池使用率过高', {
                    ...poolStatus,
                    usageRate: Math.round(usageRate * 100),
                    threshold: Math.round(usageThreshold * 100),
                    url: req.url
                });
            }
        }
        
        req.dbPoolStatus = poolStatus;
    } catch (error) {
        logger.error('数据库连接池监控失败', { error: error.message });
    }
    
    next();
};

/**
 * 缓存命中率监控中间件
 */
const cacheMonitor = async (req, res, next) => {
    try {
        const cacheStats = await cacheManager.getStats();
        req.cacheStats = cacheStats;
        
        // 这里可以添加缓存命中率的监控逻辑
        // 由于Redis的info命令返回的数据比较复杂，这里简化处理
        
    } catch (error) {
        logger.error('缓存监控失败', { error: error.message });
    }
    
    next();
};

/**
 * 系统资源监控
 */
const systemResourceMonitor = () => {
    const os = require('os');
    
    return (req, res, next) => {
        const cpuUsage = process.cpuUsage();
        const loadAverage = os.loadavg();
        const freeMemory = os.freemem();
        const totalMemory = os.totalmem();
        
        const systemData = {
            cpu: {
                user: cpuUsage.user,
                system: cpuUsage.system
            },
            loadAverage: loadAverage.map(load => Math.round(load * 100) / 100),
            memory: {
                free: Math.round(freeMemory / 1024 / 1024),
                total: Math.round(totalMemory / 1024 / 1024),
                usage: Math.round((1 - freeMemory / totalMemory) * 100)
            }
        };
        
        // 检查系统负载
        const loadThreshold = parseFloat(process.env.SYSTEM_LOAD_THRESHOLD) || 2.0;
        if (loadAverage[0] > loadThreshold) {
            logger.warn('系统负载过高', {
                ...systemData,
                threshold: loadThreshold,
                url: req.url
            });
        }
        
        req.systemResources = systemData;
        next();
    };
};

/**
 * 性能数据收集中间件
 */
const performanceCollector = (req, res, next) => {
    // 在响应结束时收集所有性能数据
    const originalEnd = res.end;
    res.end = function(...args) {
        const performanceData = {
            timestamp: new Date().toISOString(),
            request: {
                method: req.method,
                url: req.url,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                size: req.requestSize
            },
            response: {
                statusCode: res.statusCode,
                duration: req.responseTime
            },
            system: {
                memory: req.memoryUsage,
                dbPool: req.dbPoolStatus,
                cache: req.cacheStats,
                resources: req.systemResources
            }
        };
        
        // 异步保存性能数据（避免阻塞响应）
        setImmediate(() => {
            savePerformanceData(performanceData);
        });
        
        originalEnd.apply(this, args);
    };
    
    next();
};

/**
 * 保存性能数据到数据库
 * @param {Object} data - 性能数据
 */
async function savePerformanceData(data) {
    try {
        const dbConnection = require('../models/DatabaseConnection');
        
        // 保存关键性能指标到数据库
        if (data.response.duration) {
            await dbConnection.query(
                `INSERT INTO performance_stats (metric_name, metric_value, metric_unit, tags) 
                 VALUES ($1, $2, $3, $4)`,
                [
                    'api_response_time',
                    data.response.duration,
                    'ms',
                    JSON.stringify({
                        method: data.request.method,
                        url: data.request.url,
                        statusCode: data.response.statusCode
                    })
                ]
            );
        }
        
        // 保存内存使用数据
        if (data.system.memory) {
            await dbConnection.query(
                `INSERT INTO performance_stats (metric_name, metric_value, metric_unit, tags) 
                 VALUES ($1, $2, $3, $4)`,
                [
                    'memory_usage',
                    data.system.memory.heapUsed,
                    'MB',
                    JSON.stringify({ type: 'heap_used' })
                ]
            );
        }
        
    } catch (error) {
        logger.error('保存性能数据失败', { error: error.message });
    }
}

/**
 * 健康检查端点
 */
const healthCheck = async (req, res) => {
    try {
        const dbConnection = require('../models/DatabaseConnection');
        
        const health = {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            database: {
                connected: dbConnection.isConnectedToDatabase(),
                pool: dbConnection.getPoolStatus()
            },
            cache: await cacheManager.getStats()
        };
        
        // 检查数据库健康状态
        const dbHealthy = await dbConnection.healthCheck();
        if (!dbHealthy) {
            health.status = 'unhealthy';
            health.issues = ['database_connection_failed'];
        }
        
        const statusCode = health.status === 'healthy' ? 200 : 503;
        res.status(statusCode).json(health);
        
    } catch (error) {
        logger.error('健康检查失败', { error: error.message });
        res.status(503).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
};

module.exports = {
    responseTimeMonitor,
    memoryMonitor,
    requestSizeMonitor,
    dbPoolMonitor,
    cacheMonitor,
    systemResourceMonitor,
    performanceCollector,
    healthCheck
};

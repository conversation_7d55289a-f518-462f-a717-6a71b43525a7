/**
 * 优化中间件
 * 包括响应压缩、静态资源缓存、请求去重等优化功能
 */

const compression = require('compression');
const { logger } = require('../utils/logger');

/**
 * 响应压缩中间件配置
 */
const compressionMiddleware = compression({
    // 压缩级别 (1-9, 9最高压缩率但CPU消耗最大)
    level: parseInt(process.env.COMPRESSION_LEVEL) || 6,
    
    // 压缩阈值，小于此大小的响应不压缩
    threshold: parseInt(process.env.COMPRESSION_THRESHOLD) || 1024,
    
    // 过滤函数，决定哪些响应需要压缩
    filter: (req, res) => {
        // 不压缩已经压缩的内容
        if (req.headers['x-no-compression']) {
            return false;
        }
        
        // 不压缩图片和视频
        const contentType = res.getHeader('Content-Type');
        if (contentType && (
            contentType.startsWith('image/') ||
            contentType.startsWith('video/') ||
            contentType.startsWith('audio/')
        )) {
            return false;
        }
        
        // 使用默认的压缩过滤器
        return compression.filter(req, res);
    }
});

/**
 * 静态资源缓存中间件
 */
const staticCacheMiddleware = (req, res, next) => {
    // 只对静态资源设置缓存
    if (req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
        const maxAge = process.env.STATIC_CACHE_MAX_AGE || '7d';
        
        // 设置缓存头
        res.setHeader('Cache-Control', `public, max-age=${parseDuration(maxAge)}`);
        res.setHeader('Expires', new Date(Date.now() + parseDuration(maxAge) * 1000).toUTCString());
        
        // 设置ETag
        const etag = generateETag(req.url);
        res.setHeader('ETag', etag);
        
        // 检查If-None-Match头
        if (req.headers['if-none-match'] === etag) {
            res.status(304).end();
            return;
        }
    }
    
    next();
};

/**
 * API响应缓存中间件
 */
const apiCacheMiddleware = (cacheDuration = '5m') => {
    return (req, res, next) => {
        // 只对GET请求进行缓存
        if (req.method !== 'GET') {
            return next();
        }
        
        const cacheKey = generateCacheKey(req);
        const cacheManager = require('../utils/cache');
        
        // 尝试从缓存获取响应
        cacheManager.get(cacheKey).then(cachedResponse => {
            if (cachedResponse) {
                // 设置缓存命中头
                res.setHeader('X-Cache', 'HIT');
                res.setHeader('Content-Type', cachedResponse.contentType);
                
                // 返回缓存的响应
                return res.status(cachedResponse.statusCode).send(cachedResponse.data);
            }
            
            // 缓存未命中，继续处理请求
            res.setHeader('X-Cache', 'MISS');
            
            // 重写res.send方法来缓存响应
            const originalSend = res.send;
            res.send = function(data) {
                // 只缓存成功的响应
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    const responseData = {
                        statusCode: res.statusCode,
                        contentType: res.getHeader('Content-Type'),
                        data: data
                    };
                    
                    // 异步缓存响应
                    cacheManager.set(cacheKey, responseData, parseDuration(cacheDuration))
                        .catch(error => {
                            logger.error('缓存API响应失败', { 
                                cacheKey, 
                                error: error.message 
                            });
                        });
                }
                
                originalSend.call(this, data);
            };
            
            next();
        }).catch(error => {
            logger.error('获取API缓存失败', { 
                cacheKey, 
                error: error.message 
            });
            next();
        });
    };
};

/**
 * 请求去重中间件
 */
const deduplicationMiddleware = () => {
    const pendingRequests = new Map();
    
    return async (req, res, next) => {
        // 只对GET请求进行去重
        if (req.method !== 'GET') {
            return next();
        }
        
        const requestKey = generateRequestKey(req);
        
        // 检查是否有相同的请求正在处理
        if (pendingRequests.has(requestKey)) {
            const pendingRequest = pendingRequests.get(requestKey);
            
            try {
                // 等待正在处理的请求完成
                const result = await pendingRequest;
                
                // 返回相同的响应
                res.setHeader('X-Deduplicated', 'true');
                res.status(result.statusCode)
                   .set(result.headers)
                   .send(result.data);
                
                return;
            } catch (error) {
                // 如果等待的请求失败，继续处理当前请求
                logger.warn('请求去重等待失败', { 
                    requestKey, 
                    error: error.message 
                });
            }
        }
        
        // 创建一个Promise来跟踪当前请求
        const requestPromise = new Promise((resolve, reject) => {
            const originalSend = res.send;
            const originalStatus = res.status;
            let statusCode = 200;
            
            // 重写status方法
            res.status = function(code) {
                statusCode = code;
                return originalStatus.call(this, code);
            };
            
            // 重写send方法
            res.send = function(data) {
                const result = {
                    statusCode: statusCode,
                    headers: res.getHeaders(),
                    data: data
                };
                
                // 清除pending请求
                pendingRequests.delete(requestKey);
                
                // 解析Promise
                resolve(result);
                
                // 调用原始send方法
                originalSend.call(this, data);
            };
            
            // 处理错误情况
            res.on('error', (error) => {
                pendingRequests.delete(requestKey);
                reject(error);
            });
        });
        
        // 将当前请求添加到pending列表
        pendingRequests.set(requestKey, requestPromise);
        
        // 设置超时清理
        setTimeout(() => {
            if (pendingRequests.has(requestKey)) {
                pendingRequests.delete(requestKey);
            }
        }, 30000); // 30秒超时
        
        next();
    };
};

/**
 * 响应时间优化中间件
 */
const responseTimeOptimization = (req, res, next) => {
    // 设置Keep-Alive
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Keep-Alive', 'timeout=5, max=1000');
    
    // 对于API请求，设置适当的缓存策略
    if (req.url.startsWith('/api/')) {
        // 禁用缓存对于动态API
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
    }
    
    next();
};

/**
 * 内容类型优化中间件
 */
const contentTypeOptimization = (req, res, next) => {
    const originalJson = res.json;
    
    res.json = function(obj) {
        // 设置JSON响应的字符集
        this.setHeader('Content-Type', 'application/json; charset=utf-8');
        
        // 对于大型JSON响应，可以考虑流式处理
        const jsonString = JSON.stringify(obj);
        if (jsonString.length > 100000) { // 100KB
            logger.info('大型JSON响应检测', {
                url: req.url,
                size: jsonString.length,
                sizeKB: Math.round(jsonString.length / 1024)
            });
        }
        
        originalJson.call(this, obj);
    };
    
    next();
};

/**
 * 解析时间字符串为秒数
 * @param {string} duration - 时间字符串 (如 '5m', '1h', '7d')
 * @returns {number} 秒数
 */
function parseDuration(duration) {
    const units = {
        's': 1,
        'm': 60,
        'h': 3600,
        'd': 86400
    };
    
    const match = duration.match(/^(\d+)([smhd])$/);
    if (!match) return 300; // 默认5分钟
    
    const [, value, unit] = match;
    return parseInt(value) * units[unit];
}

/**
 * 生成ETag
 * @param {string} content - 内容
 * @returns {string} ETag
 */
function generateETag(content) {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(content).digest('hex');
}

/**
 * 生成缓存键
 * @param {Object} req - 请求对象
 * @returns {string} 缓存键
 */
function generateCacheKey(req) {
    const crypto = require('crypto');
    const key = `${req.method}:${req.url}:${JSON.stringify(req.query)}`;
    return crypto.createHash('md5').update(key).digest('hex');
}

/**
 * 生成请求键（用于去重）
 * @param {Object} req - 请求对象
 * @returns {string} 请求键
 */
function generateRequestKey(req) {
    const crypto = require('crypto');
    const key = `${req.method}:${req.url}:${JSON.stringify(req.query)}:${req.ip}`;
    return crypto.createHash('md5').update(key).digest('hex');
}

module.exports = {
    compressionMiddleware,
    staticCacheMiddleware,
    apiCacheMiddleware,
    deduplicationMiddleware,
    responseTimeOptimization,
    contentTypeOptimization
};

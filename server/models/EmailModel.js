/**
 * 邮件数据模型
 * 处理邮件相关的数据库操作，优化查询性能
 */

const dbConnection = require('./DatabaseConnection');
const { logger, logUtils } = require('../utils/logger');

/**
 * 邮件模型类
 */
class EmailModel {
    constructor() {
        this.connection = dbConnection;
    }

    /**
     * 创建或获取用户
     * @param {string} email - 用户邮箱
     * @returns {Promise<Object>} 用户信息
     */
    async createOrGetUser(email) {
        const domain = email.split('@')[1];
        
        try {
            // 先尝试获取用户
            let result = await this.connection.query(
                'SELECT id, email, domain FROM users WHERE email = $1',
                [email]
            );

            if (result.rows.length > 0) {
                return result.rows[0];
            }

            // 用户不存在，创建新用户
            result = await this.connection.query(
                `INSERT INTO users (email, domain) 
                 VALUES ($1, $2) 
                 RETURNING id, email, domain`,
                [email, domain]
            );

            logger.info('创建新用户', { email, domain });
            return result.rows[0];

        } catch (error) {
            logger.error('创建或获取用户失败', { 
                email, 
                error: error.message 
            });
            throw error;
        }
    }

    /**
     * 保存邮件
     * @param {Object} emailData - 邮件数据
     * @returns {Promise<Object>} 保存的邮件信息
     */
    async saveEmail(emailData) {
        const {
            messageId,
            toAddress,
            fromAddress,
            replyToAddress,
            subject,
            textContent,
            htmlContent,
            rawHeaders,
            attachments,
            dateSent,
            sizeBytes = 0
        } = emailData;

        try {
            return await this.connection.transaction(async (client) => {
                // 获取或创建用户
                const user = await this.createOrGetUser(toAddress);

                // 检查邮件是否已存在
                const existingEmail = await client.query(
                    'SELECT id FROM emails WHERE user_id = $1 AND message_id = $2',
                    [user.id, messageId]
                );

                if (existingEmail.rows.length > 0) {
                    logger.debug('邮件已存在，跳过保存', { messageId, toAddress });
                    return null;
                }

                // 保存邮件
                const emailResult = await client.query(
                    `INSERT INTO emails (
                        message_id, user_id, to_address, from_address, 
                        reply_to_address, subject, text_content, html_content,
                        raw_headers, attachments, date_sent, size_bytes
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    RETURNING id, message_id, to_address, subject, date_received`,
                    [
                        messageId, user.id, toAddress, fromAddress,
                        replyToAddress, subject, textContent, htmlContent,
                        JSON.stringify(rawHeaders || {}), 
                        JSON.stringify(attachments || []),
                        dateSent, sizeBytes
                    ]
                );

                const savedEmail = emailResult.rows[0];

                // 保存附件信息（如果有）
                if (attachments && attachments.length > 0) {
                    await this.saveAttachments(client, savedEmail.id, attachments);
                }

                logUtils.logEmailProcessing('email_saved', {
                    emailId: savedEmail.id,
                    messageId,
                    toAddress,
                    subject: subject?.substring(0, 50) + '...',
                    attachmentCount: attachments?.length || 0
                });

                return savedEmail;
            });

        } catch (error) {
            logger.error('保存邮件失败', {
                messageId,
                toAddress,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 保存附件信息
     * @param {Object} client - 数据库客户端
     * @param {string} emailId - 邮件ID
     * @param {Array} attachments - 附件列表
     */
    async saveAttachments(client, emailId, attachments) {
        for (const attachment of attachments) {
            await client.query(
                `INSERT INTO email_attachments (
                    email_id, filename, content_type, size_bytes,
                    content_id, is_inline, checksum
                ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
                [
                    emailId,
                    attachment.filename,
                    attachment.contentType,
                    attachment.size || 0,
                    attachment.contentId,
                    attachment.inline || false,
                    attachment.checksum
                ]
            );
        }
    }

    /**
     * 获取用户邮件列表（分页）
     * @param {string} userEmail - 用户邮箱
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @param {Object} filters - 过滤条件
     * @returns {Promise<Array>} 邮件列表
     */
    async getEmailsForUser(userEmail, limit = 20, offset = 0, filters = {}) {
        try {
            const user = await this.createOrGetUser(userEmail);
            
            let whereClause = 'WHERE e.user_id = $1 AND e.is_deleted = false';
            let params = [user.id];
            let paramIndex = 2;

            // 添加过滤条件
            if (filters.isRead !== undefined) {
                whereClause += ` AND e.is_read = $${paramIndex}`;
                params.push(filters.isRead);
                paramIndex++;
            }

            if (filters.isStarred !== undefined) {
                whereClause += ` AND e.is_starred = $${paramIndex}`;
                params.push(filters.isStarred);
                paramIndex++;
            }

            if (filters.fromDate) {
                whereClause += ` AND e.date_received >= $${paramIndex}`;
                params.push(filters.fromDate);
                paramIndex++;
            }

            if (filters.toDate) {
                whereClause += ` AND e.date_received <= $${paramIndex}`;
                params.push(filters.toDate);
                paramIndex++;
            }

            // 添加分页参数
            params.push(limit, offset);

            const query = `
                SELECT 
                    e.id,
                    e.message_id,
                    e.to_address,
                    e.from_address,
                    e.subject,
                    e.text_content,
                    e.html_content,
                    e.is_read,
                    e.is_starred,
                    e.date_sent,
                    e.date_received,
                    e.size_bytes,
                    (
                        SELECT COUNT(*) 
                        FROM email_attachments ea 
                        WHERE ea.email_id = e.id
                    ) as attachment_count
                FROM emails e
                ${whereClause}
                ORDER BY e.date_received DESC
                LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
            `;

            const result = await this.connection.query(query, params);
            
            logUtils.logPerformance('get_emails_for_user', Date.now(), {
                userEmail,
                resultCount: result.rows.length,
                limit,
                offset
            });

            return result.rows;

        } catch (error) {
            logger.error('获取用户邮件失败', {
                userEmail,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 获取用户邮件总数
     * @param {string} userEmail - 用户邮箱
     * @param {Object} filters - 过滤条件
     * @returns {Promise<number>} 邮件总数
     */
    async getEmailCountForUser(userEmail, filters = {}) {
        try {
            const user = await this.createOrGetUser(userEmail);
            
            let whereClause = 'WHERE user_id = $1 AND is_deleted = false';
            let params = [user.id];
            let paramIndex = 2;

            // 添加过滤条件（与getEmailsForUser保持一致）
            if (filters.isRead !== undefined) {
                whereClause += ` AND is_read = $${paramIndex}`;
                params.push(filters.isRead);
                paramIndex++;
            }

            if (filters.isStarred !== undefined) {
                whereClause += ` AND is_starred = $${paramIndex}`;
                params.push(filters.isStarred);
                paramIndex++;
            }

            if (filters.fromDate) {
                whereClause += ` AND date_received >= $${paramIndex}`;
                params.push(filters.fromDate);
                paramIndex++;
            }

            if (filters.toDate) {
                whereClause += ` AND date_received <= $${paramIndex}`;
                params.push(filters.toDate);
                paramIndex++;
            }

            const query = `SELECT COUNT(*) as total FROM emails ${whereClause}`;
            const result = await this.connection.query(query, params);
            
            return parseInt(result.rows[0].total);

        } catch (error) {
            logger.error('获取用户邮件总数失败', {
                userEmail,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 搜索邮件
     * @param {string} userEmail - 用户邮箱
     * @param {string} searchTerm - 搜索关键词
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @returns {Promise<Array>} 搜索结果
     */
    async searchEmails(userEmail, searchTerm, limit = 20, offset = 0) {
        try {
            const user = await this.createOrGetUser(userEmail);
            
            const query = `
                SELECT 
                    e.id,
                    e.message_id,
                    e.to_address,
                    e.from_address,
                    e.subject,
                    e.text_content,
                    e.html_content,
                    e.is_read,
                    e.is_starred,
                    e.date_sent,
                    e.date_received,
                    e.size_bytes,
                    ts_rank(
                        to_tsvector('english', COALESCE(e.subject, '') || ' ' || COALESCE(e.text_content, '')),
                        plainto_tsquery('english', $2)
                    ) as rank
                FROM emails e
                WHERE e.user_id = $1 
                    AND e.is_deleted = false
                    AND (
                        to_tsvector('english', COALESCE(e.subject, '') || ' ' || COALESCE(e.text_content, ''))
                        @@ plainto_tsquery('english', $2)
                        OR e.from_address ILIKE $3
                        OR e.subject ILIKE $3
                    )
                ORDER BY rank DESC, e.date_received DESC
                LIMIT $4 OFFSET $5
            `;

            const searchPattern = `%${searchTerm}%`;
            const result = await this.connection.query(query, [
                user.id, searchTerm, searchPattern, limit, offset
            ]);

            logUtils.logPerformance('search_emails', Date.now(), {
                userEmail,
                searchTerm,
                resultCount: result.rows.length
            });

            return result.rows;

        } catch (error) {
            logger.error('搜索邮件失败', {
                userEmail,
                searchTerm,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 标记邮件为已读
     * @param {string} emailId - 邮件ID
     * @param {string} userEmail - 用户邮箱
     * @returns {Promise<boolean>} 是否成功
     */
    async markEmailAsRead(emailId, userEmail) {
        try {
            const user = await this.createOrGetUser(userEmail);
            
            const result = await this.connection.query(
                'UPDATE emails SET is_read = true WHERE id = $1 AND user_id = $2',
                [emailId, user.id]
            );

            return result.rowCount > 0;

        } catch (error) {
            logger.error('标记邮件已读失败', {
                emailId,
                userEmail,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 获取用户统计信息
     * @param {string} userEmail - 用户邮箱
     * @returns {Promise<Object>} 统计信息
     */
    async getUserStats(userEmail) {
        try {
            const user = await this.createOrGetUser(userEmail);
            
            const query = `
                SELECT 
                    COUNT(*) as total_emails,
                    COUNT(*) FILTER (WHERE is_read = false) as unread_emails,
                    COUNT(*) FILTER (WHERE is_starred = true) as starred_emails,
                    COUNT(*) FILTER (WHERE date_received >= CURRENT_DATE) as today_emails,
                    COALESCE(SUM(size_bytes), 0) as total_size_bytes
                FROM emails 
                WHERE user_id = $1 AND is_deleted = false
            `;

            const result = await this.connection.query(query, [user.id]);
            const stats = result.rows[0];

            return {
                totalEmails: parseInt(stats.total_emails),
                unreadEmails: parseInt(stats.unread_emails),
                starredEmails: parseInt(stats.starred_emails),
                todayEmails: parseInt(stats.today_emails),
                totalSizeBytes: parseInt(stats.total_size_bytes)
            };

        } catch (error) {
            logger.error('获取用户统计失败', {
                userEmail,
                error: error.message
            });
            throw error;
        }
    }
}

module.exports = EmailModel;

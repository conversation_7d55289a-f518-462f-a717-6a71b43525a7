const dbConnection = require('./DatabaseConnection');
const { logger, logUtils } = require('../utils/logger');

/**
 * 数据库管理类 - PostgreSQL版本
 * 处理数据库的连接、初始化和基本操作
 */
class Database {
    constructor() {
        this.connection = dbConnection;
        this.isInitialized = false;
    }

    /**
     * 初始化数据库连接
     */
    async init() {
        try {
            if (!this.connection.isConnectedToDatabase()) {
                await this.connection.init();
            }

            // 检查数据库健康状态
            const isHealthy = await this.connection.healthCheck();
            if (!isHealthy) {
                throw new Error('数据库健康检查失败');
            }

            this.isInitialized = true;
            logger.info('数据库管理器初始化完成');

        } catch (error) {
            logger.error('数据库管理器初始化失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 创建数据表
     */
    async createTables() {
        const createEmailsTable = `
            CREATE TABLE IF NOT EXISTS emails (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                message_id TEXT UNIQUE,
                to_address TEXT NOT NULL,
                from_address TEXT,
                subject TEXT,
                text_content TEXT,
                html_content TEXT,
                date_received DATETIME DEFAULT CURRENT_TIMESTAMP,
                date_sent DATETIME,
                is_read BOOLEAN DEFAULT 0,
                attachments TEXT,
                headers TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `;

        const createIndexes = [
            'CREATE INDEX IF NOT EXISTS idx_emails_to_address ON emails(to_address)',
            'CREATE INDEX IF NOT EXISTS idx_emails_date_received ON emails(date_received)',
            'CREATE INDEX IF NOT EXISTS idx_emails_message_id ON emails(message_id)',
            'CREATE INDEX IF NOT EXISTS idx_emails_is_read ON emails(is_read)'
        ];

        return new Promise((resolve, reject) => {
            this.db.serialize(() => {
                this.db.run(createEmailsTable, (err) => {
                    if (err) {
                        console.error('创建emails表失败:', err.message);
                        reject(err);
                        return;
                    }
                });

                // 创建索引
                indexPromises = createIndexes.map(indexSql => {
                    return new Promise((resolveIndex, rejectIndex) => {
                        this.db.run(indexSql, (err) => {
                            if (err) {
                                console.error('创建索引失败:', err.message);
                                rejectIndex(err);
                            } else {
                                resolveIndex();
                            }
                        });
                    });
                });

                Promise.all(indexPromises)
                    .then(() => {
                        console.log('数据表和索引创建完成');
                        resolve();
                    })
                    .catch(reject);
            });
        });
    }

    /**
     * 执行查询
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数
     * @returns {Promise} 查询结果
     */
    async query(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    console.error('查询执行失败:', err.message);
                    console.error('SQL:', sql);
                    console.error('参数:', params);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    /**
     * 执行单行查询
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数
     * @returns {Promise} 查询结果
     */
    async queryOne(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    console.error('单行查询执行失败:', err.message);
                    console.error('SQL:', sql);
                    console.error('参数:', params);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    /**
     * 执行插入/更新/删除操作
     * @param {string} sql - SQL语句
     * @param {Array} params - 参数
     * @returns {Promise} 操作结果
     */
    async run(sql, params = []) {
        if (!this.isInitialized) {
            throw new Error('数据库未初始化');
        }

        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    console.error('操作执行失败:', err.message);
                    console.error('SQL:', sql);
                    console.error('参数:', params);
                    reject(err);
                } else {
                    resolve({
                        lastID: this.lastID,
                        changes: this.changes
                    });
                }
            });
        });
    }

    /**
     * 开始事务
     */
    async beginTransaction() {
        return this.run('BEGIN TRANSACTION');
    }

    /**
     * 提交事务
     */
    async commit() {
        return this.run('COMMIT');
    }

    /**
     * 回滚事务
     */
    async rollback() {
        return this.run('ROLLBACK');
    }

    /**
     * 执行事务
     * @param {Function} callback - 事务回调函数
     * @returns {Promise} 事务结果
     */
    async transaction(callback) {
        try {
            await this.beginTransaction();
            const result = await callback(this);
            await this.commit();
            return result;
        } catch (error) {
            await this.rollback();
            throw error;
        }
    }

    /**
     * 清理过期邮件
     * @param {number} hoursToKeep - 保留小时数
     * @returns {Promise} 清理结果
     */
    async cleanupOldEmails(hoursToKeep = 24) {
        const cutoffDate = new Date(Date.now() - hoursToKeep * 60 * 60 * 1000);
        const sql = 'DELETE FROM emails WHERE date_received < ?';
        
        try {
            const result = await this.run(sql, [cutoffDate.toISOString()]);
            console.log(`清理了 ${result.changes} 封过期邮件`);
            return result;
        } catch (error) {
            console.error('清理过期邮件失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库统计信息
     * @returns {Promise} 统计信息
     */
    async getStats() {
        try {
            const totalEmails = await this.queryOne('SELECT COUNT(*) as count FROM emails');
            const unreadEmails = await this.queryOne('SELECT COUNT(*) as count FROM emails WHERE is_read = 0');
            const todayEmails = await this.queryOne(`
                SELECT COUNT(*) as count FROM emails 
                WHERE date_received >= date('now', 'start of day')
            `);

            return {
                totalEmails: totalEmails.count,
                unreadEmails: unreadEmails.count,
                todayEmails: todayEmails.count
            };
        } catch (error) {
            console.error('获取数据库统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('关闭数据库连接失败:', err.message);
                        reject(err);
                    } else {
                        console.log('数据库连接已关闭');
                        this.isInitialized = false;
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * 检查数据库连接状态
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return this.isInitialized && this.db !== null;
    }

    /**
     * 获取数据库实例（用于直接操作）
     * @returns {sqlite3.Database} 数据库实例
     */
    getDatabase() {
        return this.db;
    }
}

// 创建单例实例
const database = new Database();

module.exports = database;

/**
 * 数据库连接管理器
 * 支持PostgreSQL，提供连接池和事务管理
 */

const { Pool } = require('pg');
const { logger, logUtils } = require('../utils/logger');

/**
 * 数据库连接管理类
 */
class DatabaseConnection {
    constructor() {
        this.pool = null;
        this.isConnected = false;
        this.config = this.buildConfig();
    }

    /**
     * 构建数据库配置
     */
    buildConfig() {
        return {
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT) || 5432,
            database: process.env.DB_NAME || 'temp_email_db',
            user: process.env.DB_USER || 'temp_email_user',
            password: process.env.DB_PASSWORD,
            ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,
            
            // 连接池配置
            min: parseInt(process.env.DB_POOL_MIN) || 2,
            max: parseInt(process.env.DB_POOL_MAX) || 10,
            acquireTimeoutMillis: 60000,
            createTimeoutMillis: 30000,
            destroyTimeoutMillis: 5000,
            idleTimeoutMillis: 30000,
            reapIntervalMillis: 1000,
            createRetryIntervalMillis: 100,
            
            // 查询超时
            statement_timeout: 30000,
            query_timeout: 30000,
            
            // 应用名称
            application_name: 'temp_email_manager'
        };
    }

    /**
     * 初始化数据库连接
     */
    async init() {
        try {
            logger.info('正在初始化数据库连接...', { config: this.sanitizeConfig() });
            
            this.pool = new Pool(this.config);
            
            // 测试连接
            const client = await this.pool.connect();
            const result = await client.query('SELECT NOW() as current_time, version() as version');
            client.release();
            
            this.isConnected = true;
            
            logger.info('数据库连接成功', {
                currentTime: result.rows[0].current_time,
                version: result.rows[0].version.split(' ')[0],
                poolSize: this.config.max
            });
            
            // 设置连接池事件监听
            this.setupPoolEvents();
            
            return true;
        } catch (error) {
            logger.error('数据库连接失败', {
                error: error.message,
                config: this.sanitizeConfig()
            });
            throw error;
        }
    }

    /**
     * 设置连接池事件监听
     */
    setupPoolEvents() {
        this.pool.on('connect', (client) => {
            logger.debug('新的数据库连接建立', {
                processId: client.processID,
                totalCount: this.pool.totalCount,
                idleCount: this.pool.idleCount
            });
        });

        this.pool.on('acquire', (client) => {
            logger.debug('连接池获取连接', {
                processId: client.processID,
                totalCount: this.pool.totalCount,
                idleCount: this.pool.idleCount
            });
        });

        this.pool.on('remove', (client) => {
            logger.debug('连接从池中移除', {
                processId: client.processID,
                totalCount: this.pool.totalCount,
                idleCount: this.pool.idleCount
            });
        });

        this.pool.on('error', (err, client) => {
            logger.error('数据库连接池错误', {
                error: err.message,
                processId: client ? client.processID : 'unknown'
            });
        });
    }

    /**
     * 清理敏感配置信息用于日志
     */
    sanitizeConfig() {
        const { password, ...safeConfig } = this.config;
        return {
            ...safeConfig,
            password: password ? '***' : undefined
        };
    }

    /**
     * 执行查询
     * @param {string} text - SQL查询语句
     * @param {Array} params - 查询参数
     * @returns {Promise<Object>} 查询结果
     */
    async query(text, params = []) {
        const start = Date.now();
        
        try {
            const result = await this.pool.query(text, params);
            const duration = Date.now() - start;
            
            logUtils.logPerformance('database_query', duration, {
                rowCount: result.rowCount,
                command: result.command
            });
            
            return result;
        } catch (error) {
            const duration = Date.now() - start;
            
            logger.error('数据库查询失败', {
                error: error.message,
                query: text,
                params: params,
                duration: `${duration}ms`
            });
            
            throw error;
        }
    }

    /**
     * 获取连接（用于事务）
     * @returns {Promise<Object>} 数据库连接
     */
    async getClient() {
        try {
            const client = await this.pool.connect();
            return client;
        } catch (error) {
            logger.error('获取数据库连接失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 执行事务
     * @param {Function} callback - 事务回调函数
     * @returns {Promise<any>} 事务结果
     */
    async transaction(callback) {
        const client = await this.getClient();
        const start = Date.now();
        
        try {
            await client.query('BEGIN');
            
            const result = await callback(client);
            
            await client.query('COMMIT');
            
            const duration = Date.now() - start;
            logUtils.logPerformance('database_transaction', duration, {
                status: 'committed'
            });
            
            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            
            const duration = Date.now() - start;
            logger.error('数据库事务失败', {
                error: error.message,
                duration: `${duration}ms`
            });
            
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 获取连接池状态
     * @returns {Object} 连接池状态
     */
    getPoolStatus() {
        if (!this.pool) {
            return { connected: false };
        }
        
        return {
            connected: this.isConnected,
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount
        };
    }

    /**
     * 健康检查
     * @returns {Promise<boolean>} 是否健康
     */
    async healthCheck() {
        try {
            const result = await this.query('SELECT 1 as health_check');
            return result.rows[0].health_check === 1;
        } catch (error) {
            logger.error('数据库健康检查失败', { error: error.message });
            return false;
        }
    }

    /**
     * 关闭连接池
     */
    async close() {
        if (this.pool) {
            try {
                await this.pool.end();
                this.isConnected = false;
                logger.info('数据库连接池已关闭');
            } catch (error) {
                logger.error('关闭数据库连接池失败', { error: error.message });
            }
        }
    }

    /**
     * 检查是否已连接
     * @returns {boolean} 连接状态
     */
    isConnectedToDatabase() {
        return this.isConnected && this.pool;
    }
}

// 创建单例实例
const dbConnection = new DatabaseConnection();

module.exports = dbConnection;

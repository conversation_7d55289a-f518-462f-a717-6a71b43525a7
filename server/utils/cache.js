/**
 * Redis缓存管理器
 * 提供高性能的缓存服务
 */

const redis = require('redis');
const { logger } = require('./logger');

/**
 * 缓存管理器类
 */
class CacheManager {
    constructor() {
        this.client = null;
        this.isConnected = false;
        this.config = this.buildConfig();
        this.defaultTTL = parseInt(process.env.REDIS_TTL) || 3600; // 1小时
    }

    /**
     * 构建Redis配置
     */
    buildConfig() {
        return {
            host: process.env.REDIS_HOST || 'localhost',
            port: parseInt(process.env.REDIS_PORT) || 6379,
            password: process.env.REDIS_PASSWORD,
            db: parseInt(process.env.REDIS_DB) || 0,
            retryDelayOnFailover: 100,
            enableReadyCheck: false,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keepAlive: 30000,
            connectTimeout: 10000,
            commandTimeout: 5000
        };
    }

    /**
     * 初始化Redis连接
     */
    async init() {
        try {
            // 如果Redis配置不完整，使用内存缓存
            if (!process.env.REDIS_HOST && process.env.NODE_ENV === 'development') {
                logger.warn('Redis未配置，使用内存缓存');
                this.useMemoryCache();
                return;
            }

            this.client = redis.createClient(this.config);

            // 设置事件监听
            this.client.on('connect', () => {
                logger.info('Redis连接建立');
            });

            this.client.on('ready', () => {
                this.isConnected = true;
                logger.info('Redis连接就绪', {
                    host: this.config.host,
                    port: this.config.port,
                    db: this.config.db
                });
            });

            this.client.on('error', (error) => {
                logger.error('Redis连接错误', { error: error.message });
                this.isConnected = false;
            });

            this.client.on('end', () => {
                logger.warn('Redis连接断开');
                this.isConnected = false;
            });

            this.client.on('reconnecting', () => {
                logger.info('Redis重新连接中...');
            });

            // 连接到Redis
            await this.client.connect();

            // 测试连接
            await this.client.ping();
            
            logger.info('Redis缓存管理器初始化完成');

        } catch (error) {
            logger.error('Redis初始化失败，降级到内存缓存', { 
                error: error.message 
            });
            this.useMemoryCache();
        }
    }

    /**
     * 使用内存缓存作为降级方案
     */
    useMemoryCache() {
        this.memoryCache = new Map();
        this.isConnected = true; // 标记为已连接，使用内存缓存
        
        // 定期清理过期的内存缓存
        setInterval(() => {
            this.cleanupMemoryCache();
        }, 60000); // 每分钟清理一次
    }

    /**
     * 清理过期的内存缓存
     */
    cleanupMemoryCache() {
        if (!this.memoryCache) return;
        
        const now = Date.now();
        for (const [key, value] of this.memoryCache.entries()) {
            if (value.expiry && value.expiry < now) {
                this.memoryCache.delete(key);
            }
        }
    }

    /**
     * 设置缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 过期时间（秒）
     * @returns {Promise<boolean>} 是否成功
     */
    async set(key, value, ttl = this.defaultTTL) {
        try {
            if (!this.isConnected) {
                return false;
            }

            const serializedValue = JSON.stringify(value);

            if (this.client) {
                await this.client.setEx(key, ttl, serializedValue);
            } else if (this.memoryCache) {
                this.memoryCache.set(key, {
                    value: serializedValue,
                    expiry: Date.now() + (ttl * 1000)
                });
            }

            return true;

        } catch (error) {
            logger.error('设置缓存失败', { 
                key, 
                error: error.message 
            });
            return false;
        }
    }

    /**
     * 获取缓存
     * @param {string} key - 缓存键
     * @returns {Promise<any>} 缓存值
     */
    async get(key) {
        try {
            if (!this.isConnected) {
                return null;
            }

            let serializedValue = null;

            if (this.client) {
                serializedValue = await this.client.get(key);
            } else if (this.memoryCache) {
                const cached = this.memoryCache.get(key);
                if (cached) {
                    if (cached.expiry && cached.expiry < Date.now()) {
                        this.memoryCache.delete(key);
                        return null;
                    }
                    serializedValue = cached.value;
                }
            }

            return serializedValue ? JSON.parse(serializedValue) : null;

        } catch (error) {
            logger.error('获取缓存失败', { 
                key, 
                error: error.message 
            });
            return null;
        }
    }

    /**
     * 删除缓存
     * @param {string} key - 缓存键
     * @returns {Promise<boolean>} 是否成功
     */
    async del(key) {
        try {
            if (!this.isConnected) {
                return false;
            }

            if (this.client) {
                await this.client.del(key);
            } else if (this.memoryCache) {
                this.memoryCache.delete(key);
            }

            return true;

        } catch (error) {
            logger.error('删除缓存失败', { 
                key, 
                error: error.message 
            });
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     * @param {string} key - 缓存键
     * @returns {Promise<boolean>} 是否存在
     */
    async exists(key) {
        try {
            if (!this.isConnected) {
                return false;
            }

            if (this.client) {
                const result = await this.client.exists(key);
                return result === 1;
            } else if (this.memoryCache) {
                const cached = this.memoryCache.get(key);
                if (cached && cached.expiry && cached.expiry < Date.now()) {
                    this.memoryCache.delete(key);
                    return false;
                }
                return this.memoryCache.has(key);
            }

            return false;

        } catch (error) {
            logger.error('检查缓存存在失败', { 
                key, 
                error: error.message 
            });
            return false;
        }
    }

    /**
     * 设置缓存过期时间
     * @param {string} key - 缓存键
     * @param {number} ttl - 过期时间（秒）
     * @returns {Promise<boolean>} 是否成功
     */
    async expire(key, ttl) {
        try {
            if (!this.isConnected) {
                return false;
            }

            if (this.client) {
                await this.client.expire(key, ttl);
            } else if (this.memoryCache) {
                const cached = this.memoryCache.get(key);
                if (cached) {
                    cached.expiry = Date.now() + (ttl * 1000);
                }
            }

            return true;

        } catch (error) {
            logger.error('设置缓存过期时间失败', { 
                key, 
                ttl, 
                error: error.message 
            });
            return false;
        }
    }

    /**
     * 批量删除缓存（按模式）
     * @param {string} pattern - 匹配模式
     * @returns {Promise<number>} 删除的数量
     */
    async delPattern(pattern) {
        try {
            if (!this.isConnected) {
                return 0;
            }

            let deletedCount = 0;

            if (this.client) {
                const keys = await this.client.keys(pattern);
                if (keys.length > 0) {
                    deletedCount = await this.client.del(keys);
                }
            } else if (this.memoryCache) {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'));
                for (const key of this.memoryCache.keys()) {
                    if (regex.test(key)) {
                        this.memoryCache.delete(key);
                        deletedCount++;
                    }
                }
            }

            return deletedCount;

        } catch (error) {
            logger.error('批量删除缓存失败', { 
                pattern, 
                error: error.message 
            });
            return 0;
        }
    }

    /**
     * 获取缓存统计信息
     * @returns {Promise<Object>} 统计信息
     */
    async getStats() {
        try {
            if (!this.isConnected) {
                return { connected: false };
            }

            if (this.client) {
                const info = await this.client.info('memory');
                const keyspace = await this.client.info('keyspace');
                
                return {
                    connected: true,
                    type: 'redis',
                    memory: info,
                    keyspace: keyspace
                };
            } else if (this.memoryCache) {
                return {
                    connected: true,
                    type: 'memory',
                    size: this.memoryCache.size
                };
            }

            return { connected: false };

        } catch (error) {
            logger.error('获取缓存统计失败', { error: error.message });
            return { connected: false, error: error.message };
        }
    }

    /**
     * 关闭连接
     */
    async close() {
        if (this.client) {
            try {
                await this.client.quit();
                logger.info('Redis连接已关闭');
            } catch (error) {
                logger.error('关闭Redis连接失败', { error: error.message });
            }
        }
        
        if (this.memoryCache) {
            this.memoryCache.clear();
        }
        
        this.isConnected = false;
    }
}

// 创建单例实例
const cacheManager = new CacheManager();

module.exports = cacheManager;

/**
 * 日志系统
 * 使用Winston实现结构化日志记录
 */

const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');

// 确保日志目录存在
const logDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

/**
 * 自定义日志格式
 */
const logFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
        
        if (Object.keys(meta).length > 0) {
            logMessage += ` ${JSON.stringify(meta)}`;
        }
        
        return logMessage;
    })
);

/**
 * 开发环境格式（更易读）
 */
const devFormat = winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({
        format: 'HH:mm:ss'
    }),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let logMessage = `${timestamp} ${level}: ${message}`;
        
        if (Object.keys(meta).length > 0) {
            logMessage += `\n${JSON.stringify(meta, null, 2)}`;
        }
        
        return logMessage;
    })
);

/**
 * 创建日志传输器
 */
const transports = [];

// 控制台输出
if (process.env.NODE_ENV !== 'production') {
    transports.push(
        new winston.transports.Console({
            format: devFormat,
            level: 'debug'
        })
    );
} else {
    transports.push(
        new winston.transports.Console({
            format: logFormat,
            level: 'info'
        })
    );
}

// 错误日志文件
transports.push(
    new DailyRotateFile({
        filename: path.join(logDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '14d',
        zippedArchive: true
    })
);

// 组合日志文件
transports.push(
    new DailyRotateFile({
        filename: path.join(logDir, 'combined-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '14d',
        zippedArchive: true
    })
);

// 访问日志文件
transports.push(
    new DailyRotateFile({
        filename: path.join(logDir, 'access-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'info',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '30d',
        zippedArchive: true
    })
);

/**
 * 创建Logger实例
 */
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    transports,
    exitOnError: false
});

/**
 * 安全日志记录器
 */
const securityLogger = winston.createLogger({
    level: 'info',
    format: logFormat,
    transports: [
        new DailyRotateFile({
            filename: path.join(logDir, 'security-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            format: logFormat,
            maxSize: '20m',
            maxFiles: '90d', // 安全日志保留更长时间
            zippedArchive: true
        })
    ]
});

/**
 * 性能日志记录器
 */
const performanceLogger = winston.createLogger({
    level: 'info',
    format: logFormat,
    transports: [
        new DailyRotateFile({
            filename: path.join(logDir, 'performance-%DATE%.log'),
            datePattern: 'YYYY-MM-DD',
            format: logFormat,
            maxSize: '20m',
            maxFiles: '7d',
            zippedArchive: true
        })
    ]
});

/**
 * 日志工具函数
 */
const logUtils = {
    /**
     * 记录API请求
     */
    logRequest(req, res, duration) {
        const logData = {
            method: req.method,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            statusCode: res.statusCode,
            duration: `${duration}ms`,
            user: req.user ? req.user.email : 'anonymous'
        };
        
        logger.info('API请求', logData);
    },

    /**
     * 记录安全事件
     */
    logSecurityEvent(event, details) {
        securityLogger.warn('安全事件', {
            event,
            ...details,
            timestamp: new Date().toISOString()
        });
    },

    /**
     * 记录登录尝试
     */
    logLoginAttempt(email, ip, success, reason = null) {
        const logData = {
            email,
            ip,
            success,
            reason,
            timestamp: new Date().toISOString()
        };
        
        if (success) {
            securityLogger.info('登录成功', logData);
        } else {
            securityLogger.warn('登录失败', logData);
        }
    },

    /**
     * 记录性能指标
     */
    logPerformance(operation, duration, details = {}) {
        performanceLogger.info('性能指标', {
            operation,
            duration: `${duration}ms`,
            ...details,
            timestamp: new Date().toISOString()
        });
    },

    /**
     * 记录错误
     */
    logError(error, context = {}) {
        logger.error('应用错误', {
            message: error.message,
            stack: error.stack,
            ...context,
            timestamp: new Date().toISOString()
        });
    },

    /**
     * 记录邮件处理
     */
    logEmailProcessing(action, details) {
        logger.info('邮件处理', {
            action,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * Express中间件：请求日志
 */
const requestLoggerMiddleware = (req, res, next) => {
    const start = Date.now();
    const originalSend = res.send;
    
    res.send = function(data) {
        const duration = Date.now() - start;
        logUtils.logRequest(req, res, duration);
        originalSend.call(this, data);
    };
    
    next();
};

/**
 * 未捕获异常处理
 */
process.on('uncaughtException', (error) => {
    logger.error('未捕获异常', {
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
    });
    
    // 给日志一些时间写入，然后退出
    setTimeout(() => {
        process.exit(1);
    }, 1000);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('未处理的Promise拒绝', {
        reason: reason.toString(),
        promise: promise.toString(),
        timestamp: new Date().toISOString()
    });
});

module.exports = {
    logger,
    securityLogger,
    performanceLogger,
    logUtils,
    requestLoggerMiddleware
};

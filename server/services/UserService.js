const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { jwtConfig, inputValidation, passwordPolicy } = require('../../config/security');

/**
 * 用户服务类 - 重构版
 * 处理用户认证、权限验证等功能，增强安全性
 */
class UserService {
    constructor() {
        this.allowedDomains = [process.env.DOMAIN || 'getmailapp.org'];
        this.jwtConfig = jwtConfig;
        this.bcryptRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;

        // 登录失败计数器（应该用Redis实现）
        this.loginAttempts = new Map();
        this.maxLoginAttempts = 5;
        this.lockoutDuration = 15 * 60 * 1000; // 15分钟
    }

    /**
     * 验证邮箱地址格式和安全性
     * @param {string} email - 邮箱地址
     * @returns {object} 验证结果
     */
    validateEmailFormat(email) {
        // 先进行XSS防护
        const sanitizedEmail = inputValidation.sanitizeHtml(email);

        // 使用安全配置中的验证规则
        const validation = inputValidation.email.validate(sanitizedEmail);

        if (!validation.isValid) {
            return {
                success: false,
                error: validation.error,
                code: 'INVALID_EMAIL_FORMAT'
            };
        }

        return {
            success: true,
            sanitizedEmail
        };
    }

    /**
     * 验证邮箱域名是否被允许
     * @param {string} email - 邮箱地址
     * @returns {object} 验证结果
     */
    validateEmailDomain(email) {
        try {
            const domain = email.split('@')[1];
            const isAllowed = this.allowedDomains.includes(domain);

            return {
                success: isAllowed,
                error: isAllowed ? null : '不支持的邮箱域名',
                code: isAllowed ? null : 'DOMAIN_NOT_ALLOWED',
                domain
            };
        } catch (error) {
            return {
                success: false,
                error: '邮箱格式错误',
                code: 'INVALID_EMAIL_FORMAT'
            };
        }
    }

    /**
     * 验证用户名格式和安全性
     * @param {string} username - 用户名
     * @returns {object} 验证结果
     */
    validateUsername(username) {
        // XSS防护
        const sanitizedUsername = inputValidation.sanitizeHtml(username);

        // 使用安全配置中的验证规则
        const validation = inputValidation.username.validate(sanitizedUsername);

        return {
            success: validation.isValid,
            error: validation.isValid ? null : validation.error,
            code: validation.isValid ? null : 'INVALID_USERNAME',
            sanitizedUsername
        };
    }

    /**
     * 检查账户是否被锁定
     * @param {string} identifier - 用户标识（邮箱或IP）
     * @returns {object} 锁定状态
     */
    checkAccountLockout(identifier) {
        const attempts = this.loginAttempts.get(identifier);

        if (!attempts) {
            return { isLocked: false };
        }

        const { count, lastAttempt } = attempts;
        const timeSinceLastAttempt = Date.now() - lastAttempt;

        // 如果锁定时间已过，清除记录
        if (timeSinceLastAttempt > this.lockoutDuration) {
            this.loginAttempts.delete(identifier);
            return { isLocked: false };
        }

        // 检查是否超过最大尝试次数
        if (count >= this.maxLoginAttempts) {
            const remainingTime = this.lockoutDuration - timeSinceLastAttempt;
            return {
                isLocked: true,
                remainingTime: Math.ceil(remainingTime / 1000 / 60), // 分钟
                message: `账户已锁定，请${Math.ceil(remainingTime / 1000 / 60)}分钟后重试`
            };
        }

        return { isLocked: false, attempts: count };
    }

    /**
     * 记录登录失败
     * @param {string} identifier - 用户标识
     */
    recordLoginFailure(identifier) {
        const attempts = this.loginAttempts.get(identifier) || { count: 0, lastAttempt: 0 };
        attempts.count += 1;
        attempts.lastAttempt = Date.now();
        this.loginAttempts.set(identifier, attempts);
    }

    /**
     * 清除登录失败记录
     * @param {string} identifier - 用户标识
     */
    clearLoginFailures(identifier) {
        this.loginAttempts.delete(identifier);
    }

    /**
     * 生成JWT访问令牌
     * @param {string} email - 用户邮箱
     * @param {string} sessionId - 会话ID
     * @returns {object} 令牌信息
     */
    generateAccessToken(email, sessionId = null) {
        const now = Math.floor(Date.now() / 1000);
        const jti = crypto.randomUUID(); // JWT ID，用于撤销

        const payload = {
            email: email,
            sessionId: sessionId || crypto.randomUUID(),
            jti: jti,
            iat: now,
            type: 'access',
            // 添加一些安全信息
            domain: email.split('@')[1]
        };

        const token = jwt.sign(payload, this.jwtConfig.secret, {
            expiresIn: this.jwtConfig.expiresIn,
            algorithm: this.jwtConfig.algorithm,
            issuer: this.jwtConfig.issuer,
            audience: this.jwtConfig.audience
        });

        return {
            token,
            jti,
            expiresAt: new Date((now + this.parseExpiryToSeconds(this.jwtConfig.expiresIn)) * 1000),
            sessionId: payload.sessionId
        };
    }

    /**
     * 生成刷新令牌
     * @param {string} email - 用户邮箱
     * @param {string} sessionId - 会话ID
     * @returns {object} 刷新令牌信息
     */
    generateRefreshToken(email, sessionId) {
        const now = Math.floor(Date.now() / 1000);
        const jti = crypto.randomUUID();

        const payload = {
            email: email,
            sessionId: sessionId,
            jti: jti,
            iat: now,
            type: 'refresh'
        };

        const token = jwt.sign(payload, this.jwtConfig.refreshSecret, {
            expiresIn: this.jwtConfig.refreshExpiresIn,
            algorithm: this.jwtConfig.algorithm,
            issuer: this.jwtConfig.issuer,
            audience: this.jwtConfig.audience
        });

        return {
            token,
            jti,
            expiresAt: new Date((now + this.parseExpiryToSeconds(this.jwtConfig.refreshExpiresIn)) * 1000)
        };
    }

    /**
     * 解析过期时间字符串为秒数
     * @param {string} expiry - 过期时间字符串（如 '24h', '7d'）
     * @returns {number} 秒数
     */
    parseExpiryToSeconds(expiry) {
        const units = {
            's': 1,
            'm': 60,
            'h': 3600,
            'd': 86400
        };

        const match = expiry.match(/^(\d+)([smhd])$/);
        if (!match) return 86400; // 默认24小时

        const [, value, unit] = match;
        return parseInt(value) * units[unit];
    }

    /**
     * 验证JWT访问令牌
     * @param {string} token - JWT令牌
     * @returns {object} 验证结果
     */
    verifyAccessToken(token) {
        try {
            // 检查令牌是否在黑名单中
            if (this.jwtConfig.isBlacklisted(token)) {
                return {
                    success: false,
                    error: '令牌已失效',
                    code: 'TOKEN_BLACKLISTED'
                };
            }

            const decoded = jwt.verify(token, this.jwtConfig.secret, {
                algorithms: [this.jwtConfig.algorithm],
                issuer: this.jwtConfig.issuer,
                audience: this.jwtConfig.audience
            });

            // 验证令牌类型
            if (decoded.type !== 'access') {
                return {
                    success: false,
                    error: '令牌类型错误',
                    code: 'INVALID_TOKEN_TYPE'
                };
            }

            // 验证域名
            if (!this.allowedDomains.includes(decoded.domain)) {
                return {
                    success: false,
                    error: '域名不被允许',
                    code: 'DOMAIN_NOT_ALLOWED'
                };
            }

            return {
                success: true,
                user: {
                    email: decoded.email,
                    domain: decoded.domain,
                    sessionId: decoded.sessionId,
                    jti: decoded.jti
                },
                decoded
            };
        } catch (error) {
            console.error('JWT验证失败:', error.message);

            let errorCode = 'TOKEN_INVALID';
            let errorMessage = '令牌无效';

            if (error.name === 'TokenExpiredError') {
                errorCode = 'TOKEN_EXPIRED';
                errorMessage = '令牌已过期';
            } else if (error.name === 'JsonWebTokenError') {
                errorCode = 'TOKEN_MALFORMED';
                errorMessage = '令牌格式错误';
            }

            return {
                success: false,
                error: errorMessage,
                code: errorCode
            };
        }
    }

    /**
     * 验证刷新令牌
     * @param {string} token - 刷新令牌
     * @returns {object} 验证结果
     */
    verifyRefreshToken(token) {
        try {
            const decoded = jwt.verify(token, this.jwtConfig.refreshSecret, {
                algorithms: [this.jwtConfig.algorithm],
                issuer: this.jwtConfig.issuer,
                audience: this.jwtConfig.audience
            });

            // 验证令牌类型
            if (decoded.type !== 'refresh') {
                return {
                    success: false,
                    error: '刷新令牌类型错误',
                    code: 'INVALID_TOKEN_TYPE'
                };
            }

            return {
                success: true,
                user: {
                    email: decoded.email,
                    sessionId: decoded.sessionId,
                    jti: decoded.jti
                },
                decoded
            };
        } catch (error) {
            console.error('刷新令牌验证失败:', error.message);

            return {
                success: false,
                error: '刷新令牌无效',
                code: 'REFRESH_TOKEN_INVALID'
            };
        }
    }

    /**
     * 撤销令牌（添加到黑名单）
     * @param {string} token - 要撤销的令牌
     * @returns {boolean} 是否成功
     */
    revokeToken(token) {
        try {
            this.jwtConfig.addToBlacklist(token);
            return true;
        } catch (error) {
            console.error('撤销令牌失败:', error.message);
            return false;
        }
    }

    /**
     * 用户登录验证
     * @param {string} email - 邮箱地址
     * @returns {object} 登录结果
     */
    async authenticateUser(email) {
        try {
            // 验证邮箱格式
            if (!this.validateEmailFormat(email)) {
                return {
                    success: false,
                    error: '邮箱地址格式不正确'
                };
            }

            // 验证域名
            if (!this.validateEmailDomain(email)) {
                return {
                    success: false,
                    error: '邮箱域名不被支持'
                };
            }

            // 提取用户名并验证
            const username = email.split('@')[0];
            if (!this.validateUsername(username)) {
                return {
                    success: false,
                    error: '用户名格式不正确，只能包含字母、数字、下划线和连字符，长度3-20位'
                };
            }

            // 生成令牌
            const token = this.generateToken(email);

            return {
                success: true,
                token: token,
                email: email,
                username: username,
                message: '登录成功'
            };

        } catch (error) {
            console.error('用户认证错误:', error);
            return {
                success: false,
                error: '认证服务暂时不可用，请稍后重试'
            };
        }
    }

    /**
     * 验证用户权限
     * @param {string} token - JWT令牌
     * @param {string} requestedEmail - 请求访问的邮箱地址
     * @returns {object} 权限验证结果
     */
    async authorizeUser(token, requestedEmail = null) {
        try {
            const decoded = this.verifyToken(token);
            
            if (!decoded) {
                return {
                    success: false,
                    error: '访问令牌无效或已过期'
                };
            }

            // 如果指定了请求的邮箱地址，检查是否匹配
            if (requestedEmail && decoded.email !== requestedEmail) {
                return {
                    success: false,
                    error: '无权访问该邮箱'
                };
            }

            return {
                success: true,
                user: {
                    email: decoded.email,
                    domain: decoded.domain,
                    username: decoded.email.split('@')[0]
                }
            };

        } catch (error) {
            console.error('用户授权错误:', error);
            return {
                success: false,
                error: '权限验证失败'
            };
        }
    }

    /**
     * 刷新令牌
     * @param {string} token - 当前令牌
     * @returns {object} 刷新结果
     */
    async refreshToken(token) {
        try {
            const decoded = this.verifyToken(token);
            
            if (!decoded) {
                return {
                    success: false,
                    error: '令牌无效，请重新登录'
                };
            }

            // 检查令牌是否即将过期（剩余时间少于1小时）
            const timeUntilExpiry = decoded.exp - Math.floor(Date.now() / 1000);
            if (timeUntilExpiry > 3600) {
                return {
                    success: true,
                    token: token,
                    message: '令牌仍然有效'
                };
            }

            // 生成新令牌
            const newToken = this.generateToken(decoded.email);

            return {
                success: true,
                token: newToken,
                message: '令牌已刷新'
            };

        } catch (error) {
            console.error('令牌刷新错误:', error);
            return {
                success: false,
                error: '令牌刷新失败'
            };
        }
    }

    /**
     * 获取用户信息
     * @param {string} token - JWT令牌
     * @returns {object} 用户信息
     */
    async getUserInfo(token) {
        try {
            const authResult = await this.authorizeUser(token);
            
            if (!authResult.success) {
                return authResult;
            }

            return {
                success: true,
                user: authResult.user
            };

        } catch (error) {
            console.error('获取用户信息错误:', error);
            return {
                success: false,
                error: '获取用户信息失败'
            };
        }
    }

    /**
     * 注销用户（将令牌加入黑名单）
     * 注意：这是一个简化实现，生产环境中应该使用Redis等缓存来维护黑名单
     * @param {string} token - JWT令牌
     * @returns {object} 注销结果
     */
    async logoutUser(token) {
        try {
            const decoded = this.verifyToken(token);
            
            if (!decoded) {
                return {
                    success: false,
                    error: '令牌无效'
                };
            }

            // 在实际应用中，这里应该将令牌加入黑名单
            // 目前只是简单返回成功
            
            return {
                success: true,
                message: '注销成功'
            };

        } catch (error) {
            console.error('用户注销错误:', error);
            return {
                success: false,
                error: '注销失败'
            };
        }
    }

    /**
     * 检查邮箱是否可用
     * @param {string} email - 邮箱地址
     * @returns {object} 检查结果
     */
    async checkEmailAvailability(email) {
        try {
            if (!this.validateEmailFormat(email)) {
                return {
                    success: false,
                    available: false,
                    error: '邮箱地址格式不正确'
                };
            }

            if (!this.validateEmailDomain(email)) {
                return {
                    success: false,
                    available: false,
                    error: '邮箱域名不被支持'
                };
            }

            const username = email.split('@')[0];
            if (!this.validateUsername(username)) {
                return {
                    success: false,
                    available: false,
                    error: '用户名格式不正确'
                };
            }

            return {
                success: true,
                available: true,
                message: '邮箱地址可用'
            };

        } catch (error) {
            console.error('检查邮箱可用性错误:', error);
            return {
                success: false,
                available: false,
                error: '检查失败'
            };
        }
    }
}

module.exports = UserService;

const Imap = require('imap');
const { simpleParser } = require('mailparser');
const EmailModel = require('../models/EmailModel');
const cacheManager = require('../utils/cache');
const { logger, logUtils } = require('../utils/logger');

/**
 * 邮件服务类 - 重构版
 * 处理邮件接收、解析、存储和查询，支持缓存和异步处理
 */
class EmailService {
    constructor() {
        this.imapConfig = {
            user: process.env.IMAP_USER,
            password: process.env.IMAP_PASSWORD,
            host: process.env.IMAP_HOST || 'imap.gmail.com',
            port: parseInt(process.env.IMAP_PORT) || 993,
            tls: process.env.IMAP_TLS === 'true',
            secure: process.env.IMAP_SECURE === 'true',
            connTimeout: parseInt(process.env.IMAP_CONNECTION_TIMEOUT) || 60000,
            authTimeout: parseInt(process.env.IMAP_AUTH_TIMEOUT) || 5000,
            tlsOptions: { rejectUnauthorized: false }
        };

        this.emailModel = new EmailModel();
        this.isConnected = false;
        this.imap = null;
        this.checkInterval = null;
        this.processingQueue = [];
        this.isProcessing = false;

        // 性能配置
        this.batchSize = parseInt(process.env.EMAIL_BATCH_SIZE) || 50;
        this.checkIntervalMs = parseInt(process.env.EMAIL_CHECK_INTERVAL) || 30000;
        this.maxRetries = parseInt(process.env.EMAIL_MAX_RETRIES) || 3;
        this.retryDelay = parseInt(process.env.EMAIL_RETRY_DELAY) || 5000;
    }

    /**
     * 初始化邮件服务
     */
    async init() {
        try {
            // 初始化缓存管理器
            await cacheManager.init();

            // 验证IMAP配置
            this.validateImapConfig();

            // 开始邮件检查
            this.startEmailCheck();

            logger.info('邮件服务初始化完成', {
                host: this.imapConfig.host,
                port: this.imapConfig.port,
                batchSize: this.batchSize,
                checkInterval: this.checkIntervalMs
            });

        } catch (error) {
            logger.error('邮件服务初始化失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 验证IMAP配置
     */
    validateImapConfig() {
        const required = ['user', 'password', 'host'];
        const missing = required.filter(key => !this.imapConfig[key]);

        if (missing.length > 0) {
            throw new Error(`IMAP配置缺失: ${missing.join(', ')}`);
        }
    }

    /**
     * 连接到IMAP服务器
     */
    async connectImap() {
        return new Promise((resolve, reject) => {
            if (this.isConnected && this.imap) {
                resolve();
                return;
            }

            this.imap = new Imap(this.imapConfig);

            this.imap.once('ready', () => {
                console.log('IMAP连接成功');
                this.isConnected = true;
                resolve();
            });

            this.imap.once('error', (err) => {
                console.error('IMAP连接错误:', err);
                this.isConnected = false;
                reject(err);
            });

            this.imap.once('end', () => {
                console.log('IMAP连接已断开');
                this.isConnected = false;
            });

            this.imap.connect();
        });
    }

    /**
     * 断开IMAP连接
     */
    async disconnectImap() {
        if (this.imap && this.isConnected) {
            this.imap.end();
            this.isConnected = false;
        }
    }

    /**
     * 开始定期检查邮件
     */
    startEmailCheck() {
        const interval = parseInt(process.env.EMAIL_CHECK_INTERVAL) || 30000; // 默认30秒
        
        this.checkInterval = setInterval(async () => {
            try {
                await this.fetchNewEmails();
            } catch (error) {
                console.error('定期邮件检查失败:', error);
            }
        }, interval);

        // 立即执行一次
        this.fetchNewEmails().catch(error => {
            console.error('初始邮件检查失败:', error);
        });
    }

    /**
     * 停止邮件检查
     */
    stopEmailCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }

    /**
     * 获取新邮件
     */
    async fetchNewEmails() {
        try {
            await this.connectImap();

            return new Promise((resolve, reject) => {
                this.imap.openBox('INBOX', false, (err, box) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    // 搜索未读邮件
                    this.imap.search(['UNSEEN'], (err, results) => {
                        if (err) {
                            reject(err);
                            return;
                        }

                        if (results.length === 0) {
                            console.log('没有新邮件');
                            resolve([]);
                            return;
                        }

                        console.log(`发现 ${results.length} 封新邮件`);

                        const fetch = this.imap.fetch(results, {
                            bodies: '',
                            markSeen: false
                        });

                        const emails = [];

                        fetch.on('message', (msg, seqno) => {
                            let buffer = '';

                            msg.on('body', (stream, info) => {
                                stream.on('data', (chunk) => {
                                    buffer += chunk.toString('utf8');
                                });

                                stream.once('end', async () => {
                                    try {
                                        const parsed = await simpleParser(buffer);
                                        const emailData = await this.processEmail(parsed);
                                        if (emailData) {
                                            emails.push(emailData);
                                        }
                                    } catch (error) {
                                        console.error('邮件解析失败:', error);
                                    }
                                });
                            });

                            msg.once('attributes', (attrs) => {
                                // 可以在这里处理邮件属性
                            });
                        });

                        fetch.once('error', (err) => {
                            console.error('邮件获取错误:', err);
                            reject(err);
                        });

                        fetch.once('end', () => {
                            console.log(`处理完成，共处理 ${emails.length} 封邮件`);
                            resolve(emails);
                        });
                    });
                });
            });

        } catch (error) {
            console.error('获取新邮件失败:', error);
            throw error;
        }
    }

    /**
     * 处理单封邮件
     * @param {object} parsed - 解析后的邮件对象
     * @returns {object|null} 处理后的邮件数据
     */
    async processEmail(parsed) {
        try {
            // 提取收件人地址
            const toAddresses = this.extractEmailAddresses(parsed.to);
            const domain = process.env.DOMAIN || 'getmailapp.org';
            
            // 过滤出属于我们域名的邮件
            const validToAddresses = toAddresses.filter(addr => 
                addr.includes(`@${domain}`)
            );

            if (validToAddresses.length === 0) {
                console.log('邮件不属于我们的域名，跳过');
                return null;
            }

            // 为每个有效的收件人地址保存邮件
            const savedEmails = [];
            for (const toAddress of validToAddresses) {
                const emailData = {
                    messageId: parsed.messageId,
                    toAddress: toAddress,
                    fromAddress: this.extractEmailAddresses(parsed.from)[0] || '',
                    subject: parsed.subject || '',
                    textContent: parsed.text || '',
                    htmlContent: parsed.html || '',
                    dateSent: parsed.date || new Date(),
                    attachments: JSON.stringify(parsed.attachments || []),
                    headers: JSON.stringify(parsed.headers || {})
                };

                const saved = await this.saveEmail(emailData);
                if (saved) {
                    savedEmails.push(saved);
                }
            }

            return savedEmails;

        } catch (error) {
            console.error('处理邮件失败:', error);
            return null;
        }
    }

    /**
     * 提取邮件地址
     * @param {string|object|array} addressField - 地址字段
     * @returns {array} 邮件地址数组
     */
    extractEmailAddresses(addressField) {
        if (!addressField) return [];

        if (typeof addressField === 'string') {
            // 简单的邮件地址提取
            const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
            return addressField.match(emailRegex) || [];
        }

        if (Array.isArray(addressField)) {
            return addressField.map(addr => 
                typeof addr === 'object' ? addr.address : addr
            ).filter(Boolean);
        }

        if (typeof addressField === 'object' && addressField.address) {
            return [addressField.address];
        }

        return [];
    }

    /**
     * 保存邮件到数据库
     * @param {object} emailData - 邮件数据
     * @returns {object|null} 保存的邮件记录
     */
    async saveEmail(emailData) {
        try {
            // 检查邮件是否已存在
            const existing = await database.queryOne(
                'SELECT id FROM emails WHERE message_id = ? AND to_address = ?',
                [emailData.messageId, emailData.toAddress]
            );

            if (existing) {
                console.log('邮件已存在，跳过保存');
                return null;
            }

            // 插入新邮件
            const result = await database.run(`
                INSERT INTO emails (
                    message_id, to_address, from_address, subject,
                    text_content, html_content, date_sent, attachments, headers
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                emailData.messageId,
                emailData.toAddress,
                emailData.fromAddress,
                emailData.subject,
                emailData.textContent,
                emailData.htmlContent,
                emailData.dateSent.toISOString(),
                emailData.attachments,
                emailData.headers
            ]);

            console.log(`邮件已保存: ${emailData.subject} -> ${emailData.toAddress}`);

            return {
                id: result.lastID,
                ...emailData
            };

        } catch (error) {
            console.error('保存邮件失败:', error);
            return null;
        }
    }

    /**
     * 获取用户的邮件列表（带缓存优化）
     * @param {string} email - 用户邮箱地址
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @param {Object} filters - 过滤条件
     * @returns {Promise<Array>} 邮件列表
     */
    async getEmailsForUser(email, limit = 20, offset = 0, filters = {}) {
        const start = Date.now();

        try {
            // 生成缓存键
            const cacheKey = this.generateCacheKey('emails', email, { limit, offset, filters });

            // 尝试从缓存获取
            let emails = await cacheManager.get(cacheKey);

            if (emails) {
                logUtils.logPerformance('get_emails_cached', Date.now() - start, {
                    userEmail: email,
                    resultCount: emails.length,
                    cacheHit: true
                });
                return emails;
            }

            // 缓存未命中，从数据库获取
            emails = await this.emailModel.getEmailsForUser(email, limit, offset, filters);

            // 格式化邮件数据
            const formattedEmails = emails.map(emailData => ({
                id: emailData.id,
                messageId: emailData.message_id,
                from: emailData.from_address,
                subject: emailData.subject,
                preview: emailData.text_content ? emailData.text_content.substring(0, 200) + '...' : '',
                date: emailData.date_received || emailData.date_sent,
                read: Boolean(emailData.is_read),
                starred: Boolean(emailData.is_starred),
                attachmentCount: emailData.attachment_count || 0,
                sizeBytes: emailData.size_bytes || 0
            }));

            // 缓存结果（5分钟）
            await cacheManager.set(cacheKey, formattedEmails, 300);

            logUtils.logPerformance('get_emails_database', Date.now() - start, {
                userEmail: email,
                resultCount: formattedEmails.length,
                cacheHit: false
            });

            return formattedEmails;

        } catch (error) {
            logger.error('获取用户邮件失败', {
                userEmail: email,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * 获取用户邮件总数
     * @param {string} email - 用户邮箱地址
     * @returns {number} 邮件总数
     */
    async getEmailCountForUser(email) {
        try {
            const result = await database.queryOne(
                'SELECT COUNT(*) as count FROM emails WHERE to_address = ?',
                [email]
            );
            return result.count;
        } catch (error) {
            console.error('获取用户邮件总数失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取邮件详情
     * @param {number} emailId - 邮件ID
     * @param {string} userEmail - 用户邮箱（用于权限验证）
     * @returns {object|null} 邮件详情
     */
    async getEmailById(emailId, userEmail) {
        try {
            const email = await database.queryOne(`
                SELECT
                    id, message_id, to_address, from_address, subject,
                    text_content, html_content, date_received, date_sent,
                    is_read, attachments, headers
                FROM emails
                WHERE id = ? AND to_address = ?
            `, [emailId, userEmail]);

            if (!email) {
                return null;
            }

            return {
                id: email.id,
                messageId: email.message_id,
                to: email.to_address,
                from: email.from_address,
                subject: email.subject,
                text: email.text_content,
                html: email.html_content,
                date: email.date_received || email.date_sent,
                read: Boolean(email.is_read),
                attachments: JSON.parse(email.attachments || '[]'),
                headers: JSON.parse(email.headers || '{}')
            };

        } catch (error) {
            console.error('获取邮件详情失败:', error);
            throw error;
        }
    }

    /**
     * 标记邮件为已读
     * @param {number} emailId - 邮件ID
     * @param {string} userEmail - 用户邮箱（用于权限验证）
     * @returns {boolean} 是否成功
     */
    async markEmailAsRead(emailId, userEmail) {
        try {
            const result = await database.run(`
                UPDATE emails
                SET is_read = 1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND to_address = ?
            `, [emailId, userEmail]);

            return result.changes > 0;

        } catch (error) {
            console.error('标记邮件已读失败:', error);
            throw error;
        }
    }

    /**
     * 删除邮件
     * @param {number} emailId - 邮件ID
     * @param {string} userEmail - 用户邮箱（用于权限验证）
     * @returns {boolean} 是否成功
     */
    async deleteEmail(emailId, userEmail) {
        try {
            const result = await database.run(
                'DELETE FROM emails WHERE id = ? AND to_address = ?',
                [emailId, userEmail]
            );

            return result.changes > 0;

        } catch (error) {
            console.error('删除邮件失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户统计信息
     * @param {string} email - 用户邮箱地址
     * @returns {object} 统计信息
     */
    async getUserStats(email) {
        try {
            const totalResult = await database.queryOne(
                'SELECT COUNT(*) as count FROM emails WHERE to_address = ?',
                [email]
            );

            const unreadResult = await database.queryOne(
                'SELECT COUNT(*) as count FROM emails WHERE to_address = ? AND is_read = 0',
                [email]
            );

            const todayResult = await database.queryOne(`
                SELECT COUNT(*) as count FROM emails
                WHERE to_address = ? AND date_received >= date('now', 'start of day')
            `, [email]);

            return {
                totalEmails: totalResult.count,
                unreadEmails: unreadResult.count,
                todayEmails: todayResult.count
            };

        } catch (error) {
            console.error('获取用户统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 搜索邮件
     * @param {string} email - 用户邮箱地址
     * @param {string} query - 搜索关键词
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @returns {array} 搜索结果
     */
    async searchEmails(email, query, limit = 20, offset = 0) {
        try {
            const searchQuery = `%${query}%`;
            const emails = await database.query(`
                SELECT
                    id, message_id, from_address, subject,
                    SUBSTR(text_content, 1, 200) as preview,
                    date_received, date_sent, is_read
                FROM emails
                WHERE to_address = ? AND (
                    subject LIKE ? OR
                    text_content LIKE ? OR
                    from_address LIKE ?
                )
                ORDER BY date_received DESC
                LIMIT ? OFFSET ?
            `, [email, searchQuery, searchQuery, searchQuery, limit, offset]);

            return emails.map(email => ({
                id: email.id,
                messageId: email.message_id,
                from: email.from_address,
                subject: email.subject,
                preview: email.preview,
                date: email.date_received || email.date_sent,
                read: Boolean(email.is_read)
            }));

        } catch (error) {
            console.error('搜索邮件失败:', error);
            throw error;
        }
    }

    /**
     * 清理过期邮件
     * @param {number} hoursToKeep - 保留小时数
     * @returns {number} 清理的邮件数量
     */
    async cleanupOldEmails(hoursToKeep = null) {
        try {
            const hours = hoursToKeep || parseInt(process.env.EMAIL_RETENTION_HOURS) || 24;
            const result = await database.cleanupOldEmails(hours);
            return result.changes;
        } catch (error) {
            console.error('清理过期邮件失败:', error);
            throw error;
        }
    }

    /**
     * 获取系统统计信息
     * @returns {object} 系统统计信息
     */
    async getSystemStats() {
        try {
            return await database.getStats();
        } catch (error) {
            console.error('获取系统统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 停止邮件服务
     */
    async stop() {
        try {
            this.stopEmailCheck();
            await this.disconnectImap();
            console.log('邮件服务已停止');
        } catch (error) {
            console.error('停止邮件服务失败:', error);
        }
    }

    /**
     * 手动触发邮件检查
     * @returns {array} 新邮件列表
     */
    async manualEmailCheck() {
        try {
            return await this.fetchNewEmails();
        } catch (error) {
            console.error('手动邮件检查失败:', error);
            throw error;
        }
    }

    /**
     * 测试IMAP连接
     * @returns {boolean} 连接是否成功
     */
    async testImapConnection() {
        try {
            await this.connectImap();
            await this.disconnectImap();
            return true;
        } catch (error) {
            logger.error('IMAP连接测试失败', { error: error.message });
            return false;
        }
    }

    /**
     * 生成缓存键
     * @param {string} type - 缓存类型
     * @param {string} userEmail - 用户邮箱
     * @param {Object} params - 参数
     * @returns {string} 缓存键
     */
    generateCacheKey(type, userEmail, params = {}) {
        const crypto = require('crypto');
        const paramString = JSON.stringify(params);
        const hash = crypto.createHash('md5').update(paramString).digest('hex');
        return `email:${type}:${userEmail}:${hash}`;
    }

    /**
     * 清除用户相关缓存
     * @param {string} userEmail - 用户邮箱
     */
    async clearUserCache(userEmail) {
        try {
            const pattern = `email:*:${userEmail}:*`;
            const deletedCount = await cacheManager.delPattern(pattern);
            logger.info('清除用户缓存', { userEmail, deletedCount });
        } catch (error) {
            logger.error('清除用户缓存失败', {
                userEmail,
                error: error.message
            });
        }
    }

    /**
     * 异步处理邮件队列
     */
    async processEmailQueue() {
        if (this.isProcessing || this.processingQueue.length === 0) {
            return;
        }

        this.isProcessing = true;
        const batchSize = Math.min(this.batchSize, this.processingQueue.length);
        const batch = this.processingQueue.splice(0, batchSize);

        try {
            const promises = batch.map(emailData =>
                this.processEmailWithRetry(emailData)
            );

            await Promise.allSettled(promises);

            logUtils.logPerformance('process_email_batch', Date.now(), {
                batchSize: batch.length,
                queueRemaining: this.processingQueue.length
            });

        } catch (error) {
            logger.error('处理邮件队列失败', { error: error.message });
        } finally {
            this.isProcessing = false;

            // 如果还有邮件待处理，继续处理
            if (this.processingQueue.length > 0) {
                setImmediate(() => this.processEmailQueue());
            }
        }
    }

    /**
     * 带重试的邮件处理
     * @param {Object} emailData - 邮件数据
     * @param {number} retryCount - 重试次数
     */
    async processEmailWithRetry(emailData, retryCount = 0) {
        try {
            await this.emailModel.saveEmail(emailData);

            // 清除相关用户的缓存
            if (emailData.toAddress) {
                await this.clearUserCache(emailData.toAddress);
            }

        } catch (error) {
            if (retryCount < this.maxRetries) {
                logger.warn('邮件处理失败，准备重试', {
                    messageId: emailData.messageId,
                    retryCount: retryCount + 1,
                    error: error.message
                });

                // 延迟后重试
                await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                return this.processEmailWithRetry(emailData, retryCount + 1);
            } else {
                logger.error('邮件处理最终失败', {
                    messageId: emailData.messageId,
                    maxRetries: this.maxRetries,
                    error: error.message
                });
            }
        }
    }
}

module.exports = EmailService;

# ===========================================
# 临时邮箱管理系统 - 环境配置文件
# ===========================================

# 应用基础配置
NODE_ENV=development
PORT=3000
APP_NAME=临时邮箱管理系统
APP_VERSION=2.0.0

# 域名配置
DOMAIN=getmailapp.org
BASE_URL=http://localhost:3000

# 数据库配置
DB_TYPE=postgresql
DB_HOST=localhost
DB_PORT=5432
DB_NAME=temp_email_db
DB_USER=temp_email_user
DB_PASSWORD=your-secure-db-password
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10

# Redis缓存配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_TTL=3600

# JWT安全配置
JWT_SECRET=your-super-secure-jwt-secret-key-at-least-32-characters-long
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key-at-least-32-characters
JWT_REFRESH_EXPIRES_IN=7d

# 邮件服务器配置
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASSWORD=your-app-password
IMAP_TLS=true
IMAP_SECURE=true
IMAP_CONNECTION_TIMEOUT=60000
IMAP_AUTH_TIMEOUT=5000

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000
SESSION_SECRET=your-session-secret-key

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=combined
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 监控配置
MONITORING_ENABLED=true
MONITORING_ENDPOINT=http://localhost:9090
MONITORING_INTERVAL=60000

# 邮件清理配置
EMAIL_RETENTION_DAYS=30
CLEANUP_INTERVAL_HOURS=24

# API配置
API_VERSION=v1
API_PREFIX=/api/v1

# 安全头配置
HELMET_CSP_ENABLED=true
HELMET_HSTS_ENABLED=true
HELMET_NOSNIFF_ENABLED=true

/**
 * UserService单元测试
 * 测试用户服务的各种功能
 */

const UserService = require('../../../server/services/UserService');

describe('UserService', () => {
  let userService;

  beforeEach(() => {
    userService = new UserService();
  });

  describe('validateEmailFormat', () => {
    test('应该验证有效的邮箱地址', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        const result = userService.validateEmailFormat(email);
        expect(result.success).toBe(true);
        expect(result.sanitizedEmail).toBe(email);
      });
    });

    test('应该拒绝无效的邮箱地址', () => {
      const invalidEmails = [
        '',
        'invalid-email',
        '@domain.com',
        'user@',
        'user@domain',
        '<EMAIL>',
        '<EMAIL>'
      ];

      invalidEmails.forEach(email => {
        const result = userService.validateEmailFormat(email);
        expect(result.success).toBe(false);
        expect(result.error).toBeDefined();
        expect(result.code).toBe('INVALID_EMAIL_FORMAT');
      });
    });

    test('应该防护XSS攻击', () => {
      const maliciousEmail = 'test<script>alert("xss")</script>@example.com';
      const result = userService.validateEmailFormat(maliciousEmail);
      
      expect(result.success).toBe(false);
      expect(result.sanitizedEmail).not.toContain('<script>');
    });

    test('应该处理null和undefined输入', () => {
      const nullResult = userService.validateEmailFormat(null);
      const undefinedResult = userService.validateEmailFormat(undefined);
      
      expect(nullResult.success).toBe(false);
      expect(undefinedResult.success).toBe(false);
    });
  });

  describe('validateEmailDomain', () => {
    test('应该验证允许的域名', () => {
      const allowedEmail = '<EMAIL>';
      const result = userService.validateEmailDomain(allowedEmail);
      
      expect(result.success).toBe(true);
      expect(result.domain).toBe('getmailapp.org');
    });

    test('应该拒绝不允许的域名', () => {
      const disallowedEmail = '<EMAIL>';
      const result = userService.validateEmailDomain(disallowedEmail);
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('不支持的邮箱域名');
      expect(result.code).toBe('DOMAIN_NOT_ALLOWED');
    });

    test('应该处理格式错误的邮箱', () => {
      const malformedEmail = 'invalid-email';
      const result = userService.validateEmailDomain(malformedEmail);
      
      expect(result.success).toBe(false);
      expect(result.code).toBe('INVALID_EMAIL_FORMAT');
    });
  });

  describe('validateUsername', () => {
    test('应该验证有效的用户名', () => {
      const validUsernames = [
        'user123',
        'test_user',
        'user-name',
        'abc',
        'a'.repeat(20) // 20个字符
      ];

      validUsernames.forEach(username => {
        const result = userService.validateUsername(username);
        expect(result.success).toBe(true);
        expect(result.sanitizedUsername).toBe(username);
      });
    });

    test('应该拒绝无效的用户名', () => {
      const invalidUsernames = [
        '',
        'ab', // 太短
        'a'.repeat(21), // 太长
        'user@name', // 包含@
        'user name', // 包含空格
        'user.name', // 包含点
        'user#name' // 包含特殊字符
      ];

      invalidUsernames.forEach(username => {
        const result = userService.validateUsername(username);
        expect(result.success).toBe(false);
        expect(result.code).toBe('INVALID_USERNAME');
      });
    });
  });

  describe('generateAccessToken', () => {
    test('应该生成有效的访问令牌', () => {
      const email = '<EMAIL>';
      const result = userService.generateAccessToken(email);
      
      expect(result).toHaveProperty('token');
      expect(result).toHaveProperty('jti');
      expect(result).toHaveProperty('expiresAt');
      expect(result).toHaveProperty('sessionId');
      
      expect(typeof result.token).toBe('string');
      expect(result.token.length).toBeGreaterThan(0);
      expect(result.jti).toBeValidUUID();
      expect(result.sessionId).toBeValidUUID();
      expect(result.expiresAt).toBeInstanceOf(Date);
    });

    test('应该生成不同的令牌', () => {
      const email = '<EMAIL>';
      const token1 = userService.generateAccessToken(email);
      const token2 = userService.generateAccessToken(email);
      
      expect(token1.token).not.toBe(token2.token);
      expect(token1.jti).not.toBe(token2.jti);
      expect(token1.sessionId).not.toBe(token2.sessionId);
    });

    test('应该使用提供的sessionId', () => {
      const email = '<EMAIL>';
      const sessionId = 'custom-session-id';
      const result = userService.generateAccessToken(email, sessionId);
      
      expect(result.sessionId).toBe(sessionId);
    });
  });

  describe('generateRefreshToken', () => {
    test('应该生成有效的刷新令牌', () => {
      const email = '<EMAIL>';
      const sessionId = 'test-session-id';
      const result = userService.generateRefreshToken(email, sessionId);
      
      expect(result).toHaveProperty('token');
      expect(result).toHaveProperty('jti');
      expect(result).toHaveProperty('expiresAt');
      
      expect(typeof result.token).toBe('string');
      expect(result.token.length).toBeGreaterThan(0);
      expect(result.jti).toBeValidUUID();
      expect(result.expiresAt).toBeInstanceOf(Date);
    });
  });

  describe('verifyAccessToken', () => {
    test('应该验证有效的访问令牌', () => {
      const email = '<EMAIL>';
      const tokenData = userService.generateAccessToken(email);
      const result = userService.verifyAccessToken(tokenData.token);
      
      expect(result.success).toBe(true);
      expect(result.user.email).toBe(email);
      expect(result.user.jti).toBe(tokenData.jti);
      expect(result.user.sessionId).toBe(tokenData.sessionId);
    });

    test('应该拒绝无效的令牌', () => {
      const invalidToken = 'invalid.token.here';
      const result = userService.verifyAccessToken(invalidToken);
      
      expect(result.success).toBe(false);
      expect(result.code).toBe('TOKEN_MALFORMED');
    });

    test('应该拒绝过期的令牌', () => {
      // 创建一个已过期的令牌
      const jwt = require('jsonwebtoken');
      const expiredToken = jwt.sign(
        { 
          email: '<EMAIL>',
          type: 'access',
          exp: Math.floor(Date.now() / 1000) - 3600 // 1小时前过期
        },
        process.env.JWT_SECRET
      );
      
      const result = userService.verifyAccessToken(expiredToken);
      
      expect(result.success).toBe(false);
      expect(result.code).toBe('TOKEN_EXPIRED');
    });

    test('应该拒绝错误类型的令牌', () => {
      const jwt = require('jsonwebtoken');
      const wrongTypeToken = jwt.sign(
        { 
          email: '<EMAIL>',
          type: 'refresh' // 错误的类型
        },
        process.env.JWT_SECRET
      );
      
      const result = userService.verifyAccessToken(wrongTypeToken);
      
      expect(result.success).toBe(false);
      expect(result.code).toBe('INVALID_TOKEN_TYPE');
    });
  });

  describe('checkAccountLockout', () => {
    test('应该返回未锁定状态对于新用户', () => {
      const identifier = '<EMAIL>';
      const result = userService.checkAccountLockout(identifier);
      
      expect(result.isLocked).toBe(false);
    });

    test('应该跟踪登录失败次数', () => {
      const identifier = '<EMAIL>';
      
      // 记录几次失败
      userService.recordLoginFailure(identifier);
      userService.recordLoginFailure(identifier);
      
      const result = userService.checkAccountLockout(identifier);
      
      expect(result.isLocked).toBe(false);
      expect(result.attempts).toBe(2);
    });

    test('应该在达到最大尝试次数后锁定账户', () => {
      const identifier = '<EMAIL>';
      
      // 记录超过最大尝试次数的失败
      for (let i = 0; i < userService.maxLoginAttempts; i++) {
        userService.recordLoginFailure(identifier);
      }
      
      const result = userService.checkAccountLockout(identifier);
      
      expect(result.isLocked).toBe(true);
      expect(result.remainingTime).toBeGreaterThan(0);
      expect(result.message).toContain('账户已锁定');
    });

    test('应该在锁定时间过后自动解锁', async () => {
      const identifier = '<EMAIL>';
      
      // 模拟锁定时间已过
      userService.lockoutDuration = 100; // 100ms用于测试
      
      // 记录失败并锁定
      for (let i = 0; i < userService.maxLoginAttempts; i++) {
        userService.recordLoginFailure(identifier);
      }
      
      // 等待锁定时间过期
      await global.testUtils.sleep(150);
      
      const result = userService.checkAccountLockout(identifier);
      
      expect(result.isLocked).toBe(false);
    });
  });

  describe('parseExpiryToSeconds', () => {
    test('应该正确解析时间字符串', () => {
      const testCases = [
        { input: '30s', expected: 30 },
        { input: '5m', expected: 300 },
        { input: '2h', expected: 7200 },
        { input: '1d', expected: 86400 }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = userService.parseExpiryToSeconds(input);
        expect(result).toBe(expected);
      });
    });

    test('应该返回默认值对于无效输入', () => {
      const invalidInputs = ['invalid', '30x', '', null, undefined];
      
      invalidInputs.forEach(input => {
        const result = userService.parseExpiryToSeconds(input);
        expect(result).toBe(86400); // 默认24小时
      });
    });
  });
});

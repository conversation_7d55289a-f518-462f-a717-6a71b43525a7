/**
 * EmailModel单元测试
 * 测试邮件数据模型的各种功能
 */

const EmailModel = require('../../../server/models/EmailModel');

// 模拟数据库连接
jest.mock('../../../server/models/DatabaseConnection', () => ({
  query: jest.fn(),
  transaction: jest.fn()
}));

// 模拟日志工具
jest.mock('../../../server/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn()
  },
  logUtils: {
    logEmailProcessing: jest.fn(),
    logPerformance: jest.fn()
  }
}));

describe('EmailModel', () => {
  let emailModel;
  let mockConnection;

  beforeEach(() => {
    emailModel = new EmailModel();
    mockConnection = require('../../../server/models/DatabaseConnection');
    
    // 重置所有模拟
    jest.clearAllMocks();
  });

  describe('createOrGetUser', () => {
    test('应该返回已存在的用户', async () => {
      const email = '<EMAIL>';
      const existingUser = {
        id: 'user-123',
        email: email,
        domain: 'example.com'
      };

      mockConnection.query.mockResolvedValueOnce({
        rows: [existingUser]
      });

      const result = await emailModel.createOrGetUser(email);

      expect(result).toEqual(existingUser);
      expect(mockConnection.query).toHaveBeenCalledWith(
        'SELECT id, email, domain FROM users WHERE email = $1',
        [email]
      );
    });

    test('应该创建新用户如果不存在', async () => {
      const email = '<EMAIL>';
      const newUser = {
        id: 'user-456',
        email: email,
        domain: 'example.com'
      };

      // 第一次查询返回空结果
      mockConnection.query.mockResolvedValueOnce({ rows: [] });
      // 第二次查询返回新创建的用户
      mockConnection.query.mockResolvedValueOnce({ rows: [newUser] });

      const result = await emailModel.createOrGetUser(email);

      expect(result).toEqual(newUser);
      expect(mockConnection.query).toHaveBeenCalledTimes(2);
      expect(mockConnection.query).toHaveBeenNthCalledWith(2,
        expect.stringContaining('INSERT INTO users'),
        [email, 'example.com']
      );
    });

    test('应该处理数据库错误', async () => {
      const email = '<EMAIL>';
      const dbError = new Error('Database connection failed');

      mockConnection.query.mockRejectedValueOnce(dbError);

      await expect(emailModel.createOrGetUser(email)).rejects.toThrow(dbError);
    });
  });

  describe('saveEmail', () => {
    test('应该成功保存新邮件', async () => {
      const emailData = global.testUtils.generateTestEmail();
      const user = { id: 'user-123', email: emailData.toAddress };
      const savedEmail = {
        id: 'email-123',
        message_id: emailData.messageId,
        to_address: emailData.toAddress,
        subject: emailData.subject,
        date_received: new Date()
      };

      // 模拟事务
      const mockClient = {
        query: jest.fn()
      };

      mockConnection.transaction.mockImplementation(async (callback) => {
        // 模拟createOrGetUser调用
        emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
        
        // 模拟检查邮件是否存在
        mockClient.query.mockResolvedValueOnce({ rows: [] });
        // 模拟保存邮件
        mockClient.query.mockResolvedValueOnce({ rows: [savedEmail] });
        
        return await callback(mockClient);
      });

      const result = await emailModel.saveEmail(emailData);

      expect(result).toEqual(savedEmail);
      expect(mockConnection.transaction).toHaveBeenCalled();
    });

    test('应该跳过已存在的邮件', async () => {
      const emailData = global.testUtils.generateTestEmail();
      const user = { id: 'user-123', email: emailData.toAddress };

      const mockClient = {
        query: jest.fn()
      };

      mockConnection.transaction.mockImplementation(async (callback) => {
        emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
        
        // 模拟邮件已存在
        mockClient.query.mockResolvedValueOnce({ 
          rows: [{ id: 'existing-email-123' }] 
        });
        
        return await callback(mockClient);
      });

      const result = await emailModel.saveEmail(emailData);

      expect(result).toBeNull();
    });

    test('应该处理保存邮件时的错误', async () => {
      const emailData = global.testUtils.generateTestEmail();
      const dbError = new Error('Failed to save email');

      mockConnection.transaction.mockRejectedValueOnce(dbError);

      await expect(emailModel.saveEmail(emailData)).rejects.toThrow(dbError);
    });
  });

  describe('getEmailsForUser', () => {
    test('应该返回用户的邮件列表', async () => {
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };
      const emails = [
        {
          id: 'email-1',
          message_id: 'msg-1',
          from_address: '<EMAIL>',
          subject: 'Test Email 1',
          text_content: 'Content 1',
          is_read: false,
          is_starred: false,
          date_received: new Date(),
          attachment_count: 0,
          size_bytes: 1024
        },
        {
          id: 'email-2',
          message_id: 'msg-2',
          from_address: '<EMAIL>',
          subject: 'Test Email 2',
          text_content: 'Content 2',
          is_read: true,
          is_starred: true,
          date_received: new Date(),
          attachment_count: 1,
          size_bytes: 2048
        }
      ];

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ rows: emails });

      const result = await emailModel.getEmailsForUser(userEmail, 20, 0);

      expect(result).toEqual(emails);
      expect(mockConnection.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT'),
        [user.id, 20, 0]
      );
    });

    test('应该支持过滤条件', async () => {
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };
      const filters = { isRead: false, isStarred: true };

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ rows: [] });

      await emailModel.getEmailsForUser(userEmail, 20, 0, filters);

      expect(mockConnection.query).toHaveBeenCalledWith(
        expect.stringContaining('AND e.is_read = $2'),
        expect.arrayContaining([user.id, false])
      );
    });

    test('应该处理查询错误', async () => {
      const userEmail = '<EMAIL>';
      const dbError = new Error('Query failed');

      emailModel.createOrGetUser = jest.fn().mockRejectedValue(dbError);

      await expect(
        emailModel.getEmailsForUser(userEmail, 20, 0)
      ).rejects.toThrow(dbError);
    });
  });

  describe('getEmailCountForUser', () => {
    test('应该返回用户的邮件总数', async () => {
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };
      const count = 42;

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ 
        rows: [{ total: count.toString() }] 
      });

      const result = await emailModel.getEmailCountForUser(userEmail);

      expect(result).toBe(count);
      expect(mockConnection.query).toHaveBeenCalledWith(
        expect.stringContaining('SELECT COUNT(*)'),
        [user.id]
      );
    });

    test('应该支持过滤条件', async () => {
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };
      const filters = { isRead: false };

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ 
        rows: [{ total: '10' }] 
      });

      const result = await emailModel.getEmailCountForUser(userEmail, filters);

      expect(result).toBe(10);
      expect(mockConnection.query).toHaveBeenCalledWith(
        expect.stringContaining('AND is_read = $2'),
        [user.id, false]
      );
    });
  });

  describe('searchEmails', () => {
    test('应该搜索用户的邮件', async () => {
      const userEmail = '<EMAIL>';
      const searchTerm = 'important';
      const user = { id: 'user-123', email: userEmail };
      const searchResults = [
        {
          id: 'email-1',
          subject: 'Important Email',
          text_content: 'This is important content',
          rank: 0.8
        }
      ];

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ rows: searchResults });

      const result = await emailModel.searchEmails(userEmail, searchTerm, 20, 0);

      expect(result).toEqual(searchResults);
      expect(mockConnection.query).toHaveBeenCalledWith(
        expect.stringContaining('plainto_tsquery'),
        [user.id, searchTerm, `%${searchTerm}%`, 20, 0]
      );
    });

    test('应该处理搜索错误', async () => {
      const userEmail = '<EMAIL>';
      const searchTerm = 'test';
      const dbError = new Error('Search failed');

      emailModel.createOrGetUser = jest.fn().mockRejectedValue(dbError);

      await expect(
        emailModel.searchEmails(userEmail, searchTerm, 20, 0)
      ).rejects.toThrow(dbError);
    });
  });

  describe('markEmailAsRead', () => {
    test('应该标记邮件为已读', async () => {
      const emailId = 'email-123';
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ rowCount: 1 });

      const result = await emailModel.markEmailAsRead(emailId, userEmail);

      expect(result).toBe(true);
      expect(mockConnection.query).toHaveBeenCalledWith(
        'UPDATE emails SET is_read = true WHERE id = $1 AND user_id = $2',
        [emailId, user.id]
      );
    });

    test('应该返回false如果邮件不存在', async () => {
      const emailId = 'nonexistent-email';
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ rowCount: 0 });

      const result = await emailModel.markEmailAsRead(emailId, userEmail);

      expect(result).toBe(false);
    });
  });

  describe('getUserStats', () => {
    test('应该返回用户统计信息', async () => {
      const userEmail = '<EMAIL>';
      const user = { id: 'user-123', email: userEmail };
      const stats = {
        total_emails: '50',
        unread_emails: '10',
        starred_emails: '5',
        today_emails: '3',
        total_size_bytes: '1048576'
      };

      emailModel.createOrGetUser = jest.fn().mockResolvedValue(user);
      mockConnection.query.mockResolvedValueOnce({ rows: [stats] });

      const result = await emailModel.getUserStats(userEmail);

      expect(result).toEqual({
        totalEmails: 50,
        unreadEmails: 10,
        starredEmails: 5,
        todayEmails: 3,
        totalSizeBytes: 1048576
      });
    });

    test('应该处理统计查询错误', async () => {
      const userEmail = '<EMAIL>';
      const dbError = new Error('Stats query failed');

      emailModel.createOrGetUser = jest.fn().mockRejectedValue(dbError);

      await expect(
        emailModel.getUserStats(userEmail)
      ).rejects.toThrow(dbError);
    });
  });
});

/**
 * Jest全局测试设置
 * 配置测试环境和全局模拟
 */

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-for-testing-only';
process.env.DOMAIN = 'test.example.com';

// 数据库配置（使用内存数据库）
process.env.DB_TYPE = 'postgresql';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_NAME = 'temp_email_test';
process.env.DB_USER = 'test_user';
process.env.DB_PASSWORD = 'test_password';

// Redis配置（测试时使用内存缓存）
process.env.REDIS_HOST = '';
process.env.REDIS_PORT = '';

// 邮件配置
process.env.IMAP_HOST = 'test.imap.com';
process.env.IMAP_PORT = '993';
process.env.IMAP_USER = '<EMAIL>';
process.env.IMAP_PASSWORD = 'test-password';
process.env.IMAP_TLS = 'true';

// 日志配置
process.env.LOG_LEVEL = 'error'; // 测试时只显示错误日志

// 全局测试工具
global.testUtils = {
  /**
   * 生成测试用的JWT令牌
   */
  generateTestToken: (email = '<EMAIL>') => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(
      { 
        email, 
        type: 'access',
        sessionId: 'test-session-id',
        jti: 'test-jti'
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
  },

  /**
   * 生成测试邮件数据
   */
  generateTestEmail: (overrides = {}) => {
    return {
      messageId: `test-${Date.now()}@test.example.com`,
      toAddress: '<EMAIL>',
      fromAddress: '<EMAIL>',
      subject: 'Test Email Subject',
      textContent: 'This is a test email content.',
      htmlContent: '<p>This is a test email content.</p>',
      dateSent: new Date(),
      attachments: [],
      rawHeaders: {},
      sizeBytes: 1024,
      ...overrides
    };
  },

  /**
   * 生成测试用户数据
   */
  generateTestUser: (overrides = {}) => {
    return {
      email: '<EMAIL>',
      domain: 'test.example.com',
      isActive: true,
      isAdmin: false,
      ...overrides
    };
  },

  /**
   * 等待指定时间
   */
  sleep: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  /**
   * 清理测试数据
   */
  cleanupTestData: async () => {
    // 这里可以添加清理测试数据的逻辑
    // 比如清理测试数据库、缓存等
  }
};

// 模拟console方法以减少测试输出噪音
const originalConsole = { ...console };
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: originalConsole.error, // 保留error输出用于调试
  debug: jest.fn()
};

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('测试中发现未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('测试中发现未捕获的异常:', error);
});

// Jest钩子
beforeAll(async () => {
  // 全局测试前置操作
});

afterAll(async () => {
  // 全局测试后置操作
  await global.testUtils.cleanupTestData();
});

beforeEach(() => {
  // 每个测试前重置模拟
  jest.clearAllMocks();
});

afterEach(() => {
  // 每个测试后清理
});

// 扩展Jest匹配器
expect.extend({
  /**
   * 检查是否为有效的邮箱地址
   */
  toBeValidEmail(received) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const pass = emailRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      };
    }
  },

  /**
   * 检查是否为有效的UUID
   */
  toBeValidUUID(received) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const pass = uuidRegex.test(received);
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid UUID`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid UUID`,
        pass: false,
      };
    }
  },

  /**
   * 检查响应时间是否在合理范围内
   */
  toBeWithinResponseTime(received, maxTime = 1000) {
    const pass = received <= maxTime;
    
    if (pass) {
      return {
        message: () => `expected ${received}ms to be greater than ${maxTime}ms`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received}ms to be within ${maxTime}ms`,
        pass: false,
      };
    }
  }
});

module.exports = {};
